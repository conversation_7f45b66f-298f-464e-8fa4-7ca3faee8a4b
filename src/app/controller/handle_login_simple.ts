import express from "express";
import jwt from "jsonwebtoken";
import { getRequestWithContext } from "../../framework/controller_express.js";
import { createController } from "../../framework/core.js";
import { isUserAdmin, isUserAdminProcPlan, isUserAdminRequisition } from "../utility/helper.js";

export const handleLoginSimple = (secretKey: string, router: express.IRouter) => {
  //

  return createController(["loginSimple"], (x) => {
    //

    router.post("/login", async (req: express.Request, res: express.Response, next: express.NextFunction) => {
      //

      const ctx = getRequestWithContext(req);

      try {
        const result = await x.loginSimple.inport(ctx, {
          username: req.body.username,
          password: req.body.password,
        });

        const payload = {
          data: {
            ...result.user,
            isAdmin: isUserAdmin(result.user.email),
            isAdminProcPlan: isUserAdminProcPlan(result.user.email),
            isAdminRequisition: isUserAdminRequisition(result.user.email),
          },
        };
        
        const expiration = { expiresIn: process.env.TOKEN_EXPIRATION };
        const token = jwt.sign(payload, secretKey, expiration);

        res.json({
          name: payload.data.name, // name user
          email: payload.data.email, // email user
          token: token,
        });
      } catch (err) {
        next(err);
      }
    });
  });

  //
};
