import { Usecase } from "../../framework/core.js";
import { DeleteApprovalGroups, getApprovalWithRequestForRuleList, SaveApprovalGroups } from "../model/model_approval.js";
import { FindApprovalTemplateGroup } from "../model/model_approval_template.js";
import { EvaluationSubmission, FindOnePQTemplate, SavePQTemplate } from "../model/model_prequalification.js";
import { FindRequisition } from "../model/model_requisition.js";
import { FindUserSupervisors } from "../model/model_user.js";
import { Vendor } from "../model/model_vendor.js";
import { DateNowHandler } from "../model/vo.js";
import { getDateOnly } from "../utility/helper.js";

class Gateways {
  findOnePQTemplate: FindOnePQTemplate;
  savePQTemplate: SavePQTemplate;
  dateNow: DateNowHandler;
  findUserSupervisors: FindUserSupervisors;
  findApprovalTemplateGroup: FindApprovalTemplateGroup;
  findRequisition: FindRequisition;
  deleteApprovalGroups: DeleteApprovalGroups;
  saveApprovalGroups: SaveApprovalGroups;
}

export class Request {
  pqId: string;
  userLogin: Vendor;
}

export class Response { }

export const pqRegistrationComplete: Usecase<Gateways, Request, Response> = {
  gateways: Gateways,
  request: Request,
  setup: (o) => async (ctx, req) => {
    const now = await o.dateNow(ctx);
// TODO: trigger complete error varchar(25)
    const pqTemplate = await o.findOnePQTemplate(ctx, { pqId: req.pqId });
    if (!pqTemplate) {
      throw new Error("pq not found");
    }

    if (!pqTemplate.pqRegistrationDate) {
      throw new Error("pq registration date not found");
    }

    // validate pq registration due date
    // if (getDateOnly(now) < pqTemplate.pqRegistrationDate) {
    //   throw new Error("pq registration date not yet expired");
    // }

    if (pqTemplate.currentPhase !== "REGISTRATION") {
      throw new Error("pq must in REGISTRATION PHASE");
    }

    pqTemplate.currentPhase = "EVALUATION"; // Submission
    pqTemplate.phasesRegistration = {
      status: "APPROVED",
      approvalGroup: null,
      isSendBack: false
    }

    // TODO: create submission / evaluation
    // TODO: create submission / evaluation approval
    const description: string = `${pqTemplate.title} - PQ Submission Phase 1`;
    const [rqs] = await o.findRequisition(ctx, { tenderCode: pqTemplate.tenderCode });
    if (!rqs || rqs.length === 0) {
      throw new Error("Requisition not found");
    }

    if (!rqs[0].requestFor) {
      throw new Error("Requisition requestFor user not found");
    }

    const approvals = await getApprovalWithRequestForRuleList(
      ctx,
      description,
      pqTemplate.assignedUser!.position!.id!,
      rqs[0].requestFor!.position!.id!,
      o.findUserSupervisors,
      o.findApprovalTemplateGroup,
      pqTemplate.id,
      "PQ_EVALUATION_1",
      new Date().getFullYear()
    );

    console.log(approvals);

    await o.deleteApprovalGroups(ctx, { documentId: pqTemplate.id, documentType: "PQ_EVALUATION_1" });

    await o.saveApprovalGroups(ctx, approvals);

    const pqEvaluation1: EvaluationSubmission = {
      response: "UNDECIDED",
      dueDate: pqTemplate.pqSubmissionDate!,
      approval: {
        status: "DRAFT",
        isSendBack: false,
        approvalGroup: approvals[0],
      }
    }

    pqTemplate.phasesEvaluation = {
      submission1: pqEvaluation1,
      submission2: null,
      submission3: null,
    }

    await o.savePQTemplate(ctx, pqTemplate);

    return {};
  },
};
