import { DataSource, DeepPartial, In, Not, <PERSON><PERSON>han } from "typeorm";
import pg from "pg";
import { Delegation, DelegationUser } from "../model/model_delegation.js";
import { Delegation as IDelegation } from "../gatewayimpl/table_delegation.js";
import { ApprovalGroup } from "../gatewayimpl/table_approval.js";
import { Log } from "../gatewayimpl/table_log.js";
import { generateID } from "../../framework/helper.js";
import { User } from "../gatewayimpl/table_user.js";
import { saveLogger, setLogger } from "../model/model_log.js";



export const importDoA = async (ds: DataSource) => {
  //
  const pool = new pg.Pool({
    host: process.env.DATABASE_RAW_HOST,
    port: Number(process.env.DATABASE_RAW_PORT),
    user: process.env.DATABASE_RAW_USER,
    password: process.env.DATABASE_RAW_PASS,
    database: process.env.DATABASE_RAW_NAME,
    connectionTimeoutMillis: 10000,
  });

  try {
    // await ds.getRepository(IDelegation).clear();
    await ds.getRepository(IDelegation).delete({
      endDate: LessThan(new Date())
    });

    if (process.env.APPLICATION_MODE !== "development") {
      // 
      console.log("Trying to connect delegation database");
  
      const query = `SELECT
          a.id, -- positionId
          CASE
          WHEN a."delegationId" IS NULL THEN 'ACTING'
            ELSE 'DELEGATION'
          END AS type,
          COALESCE(a."delegationId", a."actingId") AS delegationid, -- userId
          pos1.name AS positiondelegator,
          COALESCE(p1.name, a.id) AS delegatorname,
          p1.email AS delegatoremail,
          p2.name AS delegationname,
          p2.email AS delegationemail,
          pos2.id AS delegationpositionid,
          pos2.name AS delegationpositionname,
          a."startDate",
          a."endDate",
          a.remark
        FROM
          authority a
          LEFT JOIN person p1 ON a.id = p1."positionId"
          LEFT JOIN person p2 ON COALESCE(a."delegationId", a."actingId") = p2.id
          LEFT JOIN "position" pos1 ON a.id = pos1.id
          LEFT JOIN "position" pos2 ON p2."positionId" = pos2.id;
      `;
  
      // Get a connection from the pool
      const conn = await pool.connect();
      const result = await conn.query(query);
  
      if (!result || result.rowCount === 0) {
        console.log("Sync Delegation done, no delegation found.");
        return;
      }
  
      console.log("Sync Delegation data starting.");
  
      let delegations: Delegation[] = [];
  
      for (const row of result.rows) {
        let delegation: Delegation = {
          id: row.id, // positionId
          type: row.type,
          delegateToUserId: row.delegationid,
          delegatorPositionName: row.positiondelegator,
          delegatorName: row.delegatorname,
          delegatorEmail: row.delegatoremail,
          delegateToName: row.delegationname,
          delegateToEmail: row.delegationemail,
          delegateToUserPositionId: row.delegationpositionid,
          delegateToUserPositionName: row.delegationpositionname,
          remarks: row.remark,
          startDate: row.startDate,
          endDate: row.endDate,
          syncAt: new Date(),
        };
        delegations.push(delegation);
      }
  
      await ds.getRepository(IDelegation).save(delegations);
    }

    const newDelegations = await ds.getRepository(IDelegation).find();

    console.log("Sync Delegation done.");
    console.log("Updating Delegation User.");

    // filter by 'DELEGATION' and delegation by date (today >= startDate && today <= endDate)
    const filtered = newDelegations.filter(
      (delegation) => delegation.type === "DELEGATION"
      && new Date(delegation.endDate!) >= new Date() &&
      new Date(delegation.startDate!) <= new Date()
    );

    // revert delegated approval group
    // const revert = await revertDelegatedApprovalGroup(ds, newDelegations);
    // const updatedIds = await updateDelegateUser(ds, filtered);
    const updatedIds = await assignDelegationApproval(ds, filtered);

    await saveLogger("Sync Delegation", updatedIds, "SUCCESS");

    console.log("Update Delegation User done.");

  } catch (error) {
    //
    await saveLogger("Sync Delegation", error, "FAILED");
    console.log(`${error}`);
    throw new Error(`${error}`);
  }
};

const assignDelegationApproval = async (ds: DataSource, filteredDelegations: Delegation[]) => {
  // Assigns delegation users to approval groups
  console.log("Assigning delegation users to approval groups...");

  // get approval group with status !== DONE
  let approvalGroups = await ds.getRepository(ApprovalGroup).find({
    where: { status: Not("DONE") },
  });

  let updatedIds: string[] = [];

  // Assign new delegations
  for (const filter of filteredDelegations) {
    //
    const delegateToUser = await ds.getRepository(User).findOne({
      where: { id: filter.delegateToUserId },
      relations: {
        department: true,
        section: true,
        position: true,
      }
    });

    if (!delegateToUser || delegateToUser === null) {
      // console.log(`Delegate user with ID ${filter.delegateToUserId} not found, skipping delegation for position ${filter.id}`);
      continue;
    }

    const delegation: DelegationUser = {
      user: delegateToUser,
      startDate: filter.startDate,
      endDate: filter.endDate,
      remarks: filter.remarks,
      type: filter.type,
      syncAt: filter.syncAt,
    }

    for (const approvalGroup of approvalGroups) {
      //
      let updated = false;

      if (approvalGroup.approvals) {
        for (const approval of approvalGroup.approvals) {
          // type user
          if (approval.users && approval.users.length > 0) {
            // assign delegateToUser when delegation.id === user.position.id
            for (const user of approval.users) {
              if (
                user.position && filter.id === user.position.id &&
                user.id !== filter.delegateToUserId
              ) {
                user.delegation = delegation;
                updated = true;
                if (!updatedIds.includes(approvalGroup.id)) {
                  updatedIds.push(approvalGroup.id);
                }
                console.log(`Assigned delegation to user in approval group ${approvalGroup.id}, position ${filter.delegatorPositionName}, delegated to user ${delegateToUser.name}`);
              }
            }
          }
          // type role
          else if (approval.currentUserInPosition && approval.currentUserInPosition.position) {
            if (
              filter.id === approval.currentUserInPosition.position.id &&
              approval.currentUserInPosition.id !== filter.delegateToUserId
            ) {
              approval.currentUserInPosition.delegation = delegation;
              updated = true;
              if (!updatedIds.includes(approvalGroup.id)) {
                updatedIds.push(approvalGroup.id);
              }
              console.log(`Assigned delegation to currentUserInPosition in approval group ${approvalGroup.id}, position ${filter.delegatorPositionName}, delegated to user ${delegateToUser.name}`);
            }
          }
        }

        if (updated) {
          // reassign the JSON field to trigger update
          approvalGroup.approvals = [...approvalGroup.approvals];
          await ds.getRepository(ApprovalGroup).save(approvalGroup);
        }
      }
    }
  }

  console.log(`Assigning approval group done. Updated ${updatedIds.length} approval groups.`);

  return updatedIds;
}

const updateDelegateUser = async (ds: DataSource, filteredDelegations: Delegation[]) => {
  // get approval group with status !== DONE
  let approvalGroups = await ds.getRepository(ApprovalGroup).find({
    where: { status: Not("DONE") },
  });

  let updatedIds: string[] = [];

  // update approval group with delegation
  for (const filter of filteredDelegations) {
    approvalGroups = approvalGroups.map((ag) => ({
      ...ag,
      approvals: ag.approvals.map((app) => {
        // type user
        if (app.users && app.users.length > 0) {
          return {
            ...app,
            users: app.users.map((user) => {
              if (user.position?.id === filter.id) {
                updatedIds.push(ag.id);
                return {
                  ...user,
                  id: filter.delegateToUserId,
                };
              } else return user;
            }),
          };
        } else {
          // type role
          if (app.currentUserInPosition?.position?.id === filter.id) {
            updatedIds.push(ag.id);
            return {
              ...app,
              position: {
                ...app.position,
                id: filter.delegateToUserPositionId!,
              },
            };
          } else return app;
        }
      }),
    }));
  }

  approvalGroups
    .filter((ag) => updatedIds.includes(ag.id))
    .forEach(async (ag) => {
      await ds.getRepository(ApprovalGroup).save(ag);
    });

  return updatedIds;
}

const revertDelegatedApprovalGroup = async (ds: DataSource, filteredDelegations: Delegation[]) => {
  //
  console.log("Reverting approval group.");

  // get log from approval group
  const logs = await ds.getRepository(Log).find({
    where: { name: "Sync Delegation", status: "SUCCESS" }
  });

  // get all approval group ids
  let approvalGroupIds = logs.flatMap((log) => [...log.message]);

  // remove duplicate
  const removedDuplicateIds = [...new Set(approvalGroupIds)];

  // get all approval group by id
  const approvalGroups = await ds.getRepository(ApprovalGroup).find({
    where: { id: In(removedDuplicateIds) }
  });

  for (const approvalGroup of approvalGroups) {
    //
    let updated = false;

    if (approvalGroup.approvals) {
      for (const approval of approvalGroup.approvals) {
        // type user
        if (approval.users && approval.users.length > 0) {
          // type user find by different email & positionId
          for (const user of approval.users) {
            const originalUser = await ds.getRepository(User).findOne({ where: { email: user.email } });

            if (originalUser && user.email === originalUser.email) {
              user.id = originalUser.id;
              updated = true;
            }
          }
        }
        // type role
        else {
          // type role find by different positionId
          const currentUserInPositionId = approval.currentUserInPosition?.position!.id;
          if (approval.position!.id !== currentUserInPositionId) {
            approval.position!.id = currentUserInPositionId;
            updated = true;
          }
        }
      }

      if (updated) {
        // reassign the JSON field to trigger update
        approvalGroup.approvals = [...approvalGroup.approvals];
        await ds.getRepository(ApprovalGroup).save(approvalGroup);
      }
    }
  }

  // delete log contain updated
  await ds.getRepository(Log).delete({ name: "Sync Delegation", status: "SUCCESS" });
  console.log("Revert approval group done.");
}
