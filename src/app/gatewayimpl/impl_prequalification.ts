import { DataSource, FindOptionsWhere } from "typeorm";
import { PrequalificationTemplate, PrequalificationVendor } from "./table_prequalification.js";
import { FindOnePQTemplate, FindOnePQVendor, FindPQTemplate, FindPQVendor, FindVendorPQTemplate } from "../model/model_prequalification.js";
import { getManager } from "../../framework/gateway_typeorm.js";
import { Vendor } from "./table_vendor.js";
import { defaultFilterSize } from "./_gateway.js";
import { CIVDVendor } from "./table_civd_vendor.js";

export const implFindAllPQTemplate = (ds: DataSource): FindPQTemplate => {
  return async (ctx, filter) => {
    // =====

    let sqb = getManager(ctx, ds) //
      .getRepository(PrequalificationTemplate)
      .createQueryBuilder();

    if (filter?.as === "btb") {
      sqb = sqb.where(`"requestedBackToBacks" @> :user`, { user: JSON.stringify([{ id: filter.assignedUserId }]) });

      //
    } else if (filter?.assignedUserId) {
      sqb = sqb.where(`"assignedUser" @> :user`, { user: { id: filter.assignedUserId } });

      //
    }

    if (filter?.keyword) {
      sqb = sqb
        .where(`"tenderCode" ILIKE :tenderCode`, { tenderCode: `%${filter.keyword}%` })
        .orWhere(`"title" ILIKE :title`, { title: `%${filter.keyword}%` });
    }

    if (filter?.tenderCode) {
      sqb = sqb.andWhere(`"tenderCode" = :tenderCode`, { tenderCode: filter.tenderCode });
    }

    if (filter?.currentPhase) {
      sqb = sqb.andWhere(`"currentPhase" = :currentPhase`, { currentPhase: filter.currentPhase });
    }

    // if (filter?.pqState) {
    //   sqb = sqb.where(`"pqState" = :pqState`, { currentPhase: filter.pqState });
    // }

    // if (filter?.prequalificationType) {
    //   sqb = sqb.where(`"prequalificationType" = :prequalificationType`, { prequalificationType: filter.prequalificationType });
    // }

    const size = filter?.size || defaultFilterSize;
    const page = (filter?.page && filter?.page < 1 ? 1 : filter?.page) || 1;

    sqb = sqb.take(size);
    sqb = sqb.skip(size * (page - 1));

    const result = await sqb.getManyAndCount();

    return result;
  };
};

export const ImplFindOnePQTemplate = (ds: DataSource): FindOnePQTemplate => {
  return async (ctx, req) => {
    return await getManager(ctx, ds).getRepository(PrequalificationTemplate).findOneBy({ id: req.pqId });
  };
};

export const implFindAllPQVendor = (ds: DataSource): FindPQVendor => {
  // find registered PQ for vendor
  return async (ctx, filter) => {
    // =====
    let sqb = getManager(ctx, ds) //
      .getRepository(PrequalificationVendor)
      .createQueryBuilder("prequalification_vendor");

    if (filter.pqId) {
      sqb.where(`"prequalificationTemplate"."id" = :pqId`, { pqId: filter.pqId });
    }

    if (filter.civdVendorId) {
      sqb.where("prequalification_vendor.civdVendorId = :civdVendorId", { civdVendorId: filter.civdVendorId });
    }

    if (filter.title) {
      sqb
        .orWhere(`"pqTemplate"."title" ilike :title`, { title: `%${filter.title}%` })
        .orWhere(`"pqTemplate"."tenderCode" ilike :tenderCode`, { tenderCode: `%${filter.title}%` });
    }

    sqb.leftJoinAndMapOne(
      "prequalification_vendor.prequalificationTemplate",
      PrequalificationTemplate,
      "prequalificationTemplate",
      "prequalificationTemplate.id = prequalification_vendor.prequalificationTemplateId"
    );
    sqb.leftJoinAndMapOne("prequalification_vendor.vendor", CIVDVendor, "vendor", "vendor.civdVendorId = prequalification_vendor.civdVendorId");

    const size = filter?.size || defaultFilterSize;
    const page = (filter?.page && filter?.page < 1 ? 1 : filter?.page) || 1;

    sqb = sqb.skip(size * (page - 1));

    const result = await sqb.getManyAndCount();

    return result;
  };
};

export const implFindOnePQVendor = (ds: DataSource): FindOnePQVendor => {
  return async (ctx, req) => {
    let where: FindOptionsWhere<PrequalificationVendor> | [] = {};

    if (req?.vendorPqId) where.id = req.vendorPqId;
    if (req?.civdVendorId) where.civdVendorId = req.civdVendorId;
    if (req?.pqId) where.prequalificationTemplate = { id: req.pqId };

    return await getManager(ctx, ds)
      .getRepository(PrequalificationVendor)
      .findOne({
        where,
        relations: { civdVendor: true, prequalificationTemplate: true },
      });
  };
};

export const implFindVendorPQTemplate = (ds: DataSource): FindVendorPQTemplate => {
  // find available PQ for vendor to registration
  return async (ctx, req) => {
    let sqb = getManager(ctx, ds) //
      .getRepository(PrequalificationTemplate)
      .createQueryBuilder("prequalification_template");

    if (req.listType === "pqInfo") {
      sqb
        .where((qb) => {
          const subQuery = qb
            .subQuery()
            .select("vendor.prequalificationTemplateId")
            .from(PrequalificationVendor, "vendor")
            .where("vendor.civdVendorId = :civdVendorId", { civdVendorId: req.civdVendorId })
            .getQuery();
          return "prequalification_template.id NOT IN " + subQuery;
        })
        .orWhere(
          ` 
        CASE 
            WHEN prequalification_template."prequalificationType" = 'ANNOUNCEMENT' THEN true
            WHEN prequalification_template."prequalificationType" = 'INVITATION' AND prequalification_template."invitedVendors" @> :invitedVendors THEN true
            ELSE false
        END
      `,
          { invitedVendors: JSON.stringify([req.civdVendorId]) }
        );
    } else {
      sqb.andWhere((qb) => {
        const subQuery = qb
          .subQuery()
          .select("vendor.prequalificationTemplateId")
          .from(PrequalificationVendor, "vendor")
          .where("vendor.civdVendorId = :civdVendorId", { civdVendorId: req.civdVendorId })
          .getQuery();
        return "prequalification_template.id IN " + subQuery;
      });
    }

    if (req.currentPhase) {
      sqb.andWhere(`prequalification_template.currentPhase ilike :currentPhase`, { currentPhase: req.currentPhase });
    }

    if (req.title) {
      sqb
        .andWhere(`"prequalification_template"."title" ilike :title`, { title: `%${req.title}%` })
        .orWhere(`"tenderCode" ilike :tenderCode`, { tenderCode: `%${req.title}%` });
    }

    // .where("ANNOUNCEMENT"); // announcement
    // sqb.orWhere(`"prequalification_template"."invitedVendors" @> :vendor`, { vendor: JSON.stringify({ id: req.vendorId }) }); // invitation

    const result = await sqb.getManyAndCount();

    return result;
  };
};

// FindVendorPQTemplate
