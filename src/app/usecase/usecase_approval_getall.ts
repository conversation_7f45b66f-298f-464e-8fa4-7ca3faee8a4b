import { Usecase } from "../../framework/core.js";
import { InputResponseWithCount } from "../../framework/repository.js";
import { Approval, ApprovalGroup, FindApprovalGroup, FindApprovalGroupFilter } from "../model/model_approval.js";
import { ApprovalTemplateRule } from "../model/model_approval_template.js";
import { isOperationalServiceDepartment } from "../model/model_department.js";
import { Position } from "../model/model_position.js";
import { FindRequisition, Requisition } from "../model/model_requisition.js";
import { User } from "../model/model_user.js";
import { ApprovalStatus, SubDocumentRequisition, TypeOf, UserRole } from "../model/vo.js";

class Gateways {
  findApprovalGroup: FindApprovalGroup;
  findRequisition: FindRequisition;
}

export class Request extends FindApprovalGroupFilter { }

export class Response extends InputResponseWithCount<ApprovalHeader> { }

export const approvalGetAll: Usecase<Gateways, Request, Response> = {
  gateways: Gateways,
  request: Request,
  setup: (o) => async (ctx, req) => {
    const [items] = await o.findApprovalGroup(ctx, req);

    const minRQValue = 100_000;
    const maxRQValue = 300_000;

    const minOEValue = 20_000;
    const maxOEValue = 50_000;

    const minDAValue = 5_000_000;
    const maxDAValue = 5_000_000;
    const valuePercentage = 10 / 100;

    if (req.documentType === "REQUISITION") {
      //

      const [rqs, count] = await o.findRequisition(ctx, {
        id: req.documentId
      });

      if (count === 0) {
        throw new Error("REQUISITION not found");
      }

      const requisition = rqs[0];
      const newApprovalGroup: ApprovalGroup[] = [];
      const newApprovalDetail: ApprovalDetail[] = [];

      const docRule: ApprovalTemplateRule = {
        documentType: "REQUISITION",
        commodity: requisition.commodity,
        commodityServiceType: isOperationalServiceDepartment(requisition.commodity, requisition.department!.code!),
        budgetOwners: requisition.budgetOwners,
        isAdditionalProcPlan: requisition.isAdditionalProcPlan,
        isRequestFor: true,
        isCommodityGoodsAndFirmCommitment: requisition.commodity === "GOODS" && requisition.contractTypeEngagement === "FIRM_COMMITMENT",
        isDaJustification: requisition.tenderMethod === "DIRECT_APPOINTMENT" && requisition.value > minRQValue,
        isValueGreaterThanMinOeValue: requisition.value > minOEValue, // 20_000
        isValueGreaterThanMaxOeValue: requisition.value > maxOEValue, // 50_000
        isValueGreaterThanMinRequisitionValue: requisition.value > minRQValue,
        isValueGreaterThanMaxDaValue: requisition.value > maxDAValue, // 5_000_000
        isAdditionalProcPlanOrValueMoreThanPercentage: requisition.isAdditionalProcPlan || requisition.value > requisition.procPlanValue! + requisition.procPlanValue! * valuePercentage // 10%
      };

      let OEValue = requisition.value;

      if (requisition.currency === "IDR") {
        OEValue = OEValue / 10_000;
      }

      // reconstruct approval list
      {
        for (const item of items) {
          const newApprovals: Approval[] = [];
          for (const app of item.approvals) {
            if (UserRole.some((x) => x === app.currentUserInPosition?.position?.role)) {
              newApprovalDetail.push({
                as: app.as,
                date: app.date,
                signer: app.signer ? app.signer.name : "",
                status: app.status,
                users: app.users ? app.users : [],
                position: app.position ? app.position : null,
                currentUserInPosition: app.currentUserInPosition ? app.currentUserInPosition : null,
                sequence: item.sequence,
              });
            } else {
              newApprovals.push(app);
            }
          }
          newApprovalGroup.push({
            approvals: newApprovals,
            sequence: item.sequence,
          });
        }
      }

      const result: ApprovalHeader[] = SubDocumentRequisition.map((subDocumentType) => {
        return {
          subDocumentType,
          approvals: [
            ...newApprovalDetail,
            ...newApprovalGroup.flatMap((it) =>
              it.approvals
                .filter((x) => x.subDocumentType === subDocumentType)
                .map((app) => ({
                  as: app.as,
                  date: app.date,
                  signer: app.signer ? app.signer.name : "",
                  status: app.status,
                  users: app.users ? app.users : [],
                  position: app.position ? app.position : null,
                  currentUserInPosition: app.currentUserInPosition ? app.currentUserInPosition : null,
                  sequence: it.sequence,
                }))
            ),
          ].sort((a, b) => a.sequence! - b.sequence!),
        };
      });

      // const filteredByRuleResult: ApprovalHeader[] = result.map((header) => ({
      //   subDocumentType: header.subDocumentType,
      //   approvals: header.approvals
      //     .filter((app) => !(header.subDocumentType === "INSURANCE_ASSESSMENT" && !!app.currentUserInPosition))
      //     .filter((app) => !(header.subDocumentType === "HSE_RISK_ASSESSMENT" && app.currentUserInPosition?.position?.role === "GM"))
      //     .filter((app) => !(header.subDocumentType === "OWNER_ESTIMATION" && app.currentUserInPosition?.position?.role === "GM" && OEValue <= minOEValue))
      //     .filter((app) => !(header.subDocumentType === "REQUISITION" && app.currentUserInPosition?.position?.role === "GM" && OEValue <= maxRQValue))
      //     .filter(
      //       (app) =>
      //         !(
      //           header.subDocumentType === "REQUISITION" &&
      //           (app.currentUserInPosition?.position?.role === "SMVP" || app.currentUserInPosition?.position?.role === "GM") &&
      //           OEValue <= minRQValue
      //         )
      //     )
      //     .filter(
      //       (app) =>
      //         !(
      //           header.subDocumentType === "REQUISITION" &&
      //           !!app.currentUserInPosition &&
      //           app.currentUserInPosition?.position?.role !== "STAFF" &&
      //           requisition.commodity === "GOODS" &&
      //           requisition.contractTypeEngagement === "FIRM_COMMITMENT"
      //         )
      //     ),
      // }));

      const isAdditionalProcPlanOrValueMoreThan10 = requisition.isAdditionalProcPlan || OEValue > (requisition.procPlanValue || 0) + (requisition.procPlanValue || 0) * 0.1;

      const filteredByRuleResult: ApprovalHeader[] = result.map((header) => ({
        subDocumentType: header.subDocumentType,
        approvals: header.approvals
          .filter((app) => !(header.subDocumentType === "INSURANCE_ASSESSMENT" && ["GM", "SMVP"].includes(app.currentUserInPosition?.position?.role ?? "")))
          .filter((app) => !(header.subDocumentType === "HSE_RISK_ASSESSMENT" && ["GM", "SMVP"].includes(app.currentUserInPosition?.position?.role ?? "")))
          .filter((app) => !(header.subDocumentType === "DA_JUSTIFICATION" && !!app.currentUserInPosition && !docRule.isDaJustification))
          .filter(
            (app) =>
              !(
                header.subDocumentType === "OWNER_ESTIMATION" &&
                ((app.currentUserInPosition?.position?.role === "GM" && OEValue <= maxOEValue) ||
                  (app.currentUserInPosition?.position?.role === "SMVP" && OEValue <= minOEValue))
              )
          )
          .filter(
            (app) =>
              !(
                header.subDocumentType === "REQUISITION" &&
                !docRule.isAdditionalProcPlanOrValueMoreThanPercentage &&
                app.currentUserInPosition?.position?.role === "GM" &&
                OEValue <= maxRQValue
              )
          )
          .filter(
            (app) =>
              !(
                header.subDocumentType === "REQUISITION" &&
                !docRule.isAdditionalProcPlanOrValueMoreThanPercentage &&
                (app.currentUserInPosition?.position?.role === "SMVP" || app.currentUserInPosition?.position?.role === "GM") &&
                OEValue <= minRQValue
              )
          )
          .filter(
            (app, index) =>
              !(
                header.subDocumentType === "REQUISITION" &&
                !docRule.isAdditionalProcPlanOrValueMoreThanPercentage &&
                index !== 0 &&
                app.currentUserInPosition &&
                requisition.commodity === "GOODS" &&
                requisition.contractTypeEngagement === "FIRM_COMMITMENT"
              )
          ),
      }));

      return {
        items: filteredByRuleResult,
        count: filteredByRuleResult.length,
      };
    }

    return {
      items: [
        {
          subDocumentType: null,
          approvals: items.flatMap((it) =>
            it.approvals.map((app) => ({
              as: app.as,
              date: app.date,
              signer: app.signer ? app.signer.name : "",
              status: app.status,
              users: app.users ? app.users : [],
              position: app.position ? app.position : null,
              currentUserInPosition: app.currentUserInPosition ? app.currentUserInPosition : null,
              sequence: it.sequence,
            }))
          ),
        },
      ],
      count: 1,
    };
  },
};

type ApprovalHeader = {
  subDocumentType: TypeOf<typeof SubDocumentRequisition> | null;
  approvals: ApprovalDetail[];
};

type ApprovalDetail = {
  as?: string | null;
  date?: Date | null;
  signer?: string | null;
  status?: TypeOf<typeof ApprovalStatus>;
  users?: User[];
  position?: Position | null;
  currentUserInPosition?: User | null;
  sequence?: number;
};
