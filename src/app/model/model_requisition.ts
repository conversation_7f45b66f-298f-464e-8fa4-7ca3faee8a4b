import { ActionHandler, Context } from "../../framework/core.js";
import { BaseFindManyFilter, DeleteEntity, FindManyEntity, SaveEntity } from "../../framework/repository.js";
import { isUserAdmin } from "../utility/helper.js";
import { MailTableData, sendApprovalMail } from "../utility/mailer_2.js";
import { activeDelegation, Approval, ApprovalGroup, approvalUserActiveDelegation, getSubDocumentWording, HasApprovalGroup } from "./model_approval.js";
import { ApprovalTemplateGroup } from "./model_approval_template.js";
import { DelegationUser } from "./model_delegation.js";
import { Department } from "./model_department.js";
import { PrItem } from "./model_pr_item.js";
import { Section } from "./model_section.js";
import { User } from "./model_user.js";
import {
  Commodity,
  Consequence,
  Currency,
  DocumentStatus,
  EligibilityActionWithCanAssign,
  Files,
  Incoterms,
  TenderMethod,
  TypeOf,
  validateActionByID,
  ContractTypePayment,
  ContractTypeEngagement,
  PotentialDamageType,
  PotentialDamageParty,
  SharepointFile,
  ValueCurrency,
  formatNumber,
  SubDocumentRequisition,
} from "./vo.js";

export class Requisition extends HasApprovalGroup {
  id: string;
  year: number;
  durationDays?: number | 0;

  // step 1 general
  submittedDate: Date | null;
  department: Department | null;
  section: Section | null;
  creator: User | null;
  requestFor: User | null;
  requesterBackToBack: User[];
  membersInvolved: User[];
  submitter: User | null;

  assignedUser?: User | null;
  assignedBackToBack?: User[];

  // step 1 detail
  procPlanDetailId: string; // Kalau kosong berarti additional
  procPlanDetailCode: string; // Kalau kosong berarti additional
  isAdditionalProcPlan: boolean;
  additionalProcPlanExplanation: string[];
  procPlanDetails: string[];

  title: string;
  generalScopeOfWork: string;
  justificationOfRequiredWorks: string;
  commodity: TypeOf<typeof Commodity>;
  tenderMethod: TypeOf<typeof TenderMethod>;
  contractTypePayment: TypeOf<typeof ContractTypePayment>;
  contractTypeEngagement: TypeOf<typeof ContractTypeEngagement>;
  incoterms: TypeOf<typeof Incoterms>;
  importation: boolean;
  localContentLevel: number;
  contractDateStart: Date | null;
  contractDateEnd: Date | null;
  poDateIssuance: Date | null;
  poDateDelivery: Date | null;
  durationType: string;
  durationValue: string;
  purchaseRequisition?: PrItem[];
  tenderCode?: string;

  procPlanCurrency?: string | null;
  procPlanValue?: number | null;

  // step 1 oe
  currency: TypeOf<typeof Currency>;
  value: number;
  budgetOwners: string[];
  budgetOwnerDetails: BudgetOwnerDetail[] | null;
  budgetOwnerPSC: BudgetOwnerPSC | null;
  oeReference: OEReference | null;

  // step 1 hse risk
  typeWork: string;
  hseAssessment: string;
  hseRiskCategory: HSERiskCategory[];

  // step 1 location of work
  workOfLocation: string[];

  // step 1 insurance
  userInsurance: UserInsurance | null;
  legalInsurance: LegalInsurance | null;

  // step 2 tender
  tenderBidder: TenderBidder | null;

  // step 2 supporting documents
  supportingDocuments: SupportingDocuments | null;

  checkValidation?: boolean;
}

export type RequisitionPayload = Omit<
  Requisition,
  | "id"
  | "submittedDate"
  | "department"
  | "creator"
  | "requestFor"
  | "requesterBackToBack"
  | "membersInvolved"
  | "submitter"
  | "assignedUser"
  | "assignedBackToBack"
>;

// Type for admin update requisition payload that excludes specific fields
export type AdminUpdateRequisitionPayload = Omit<
  RequisitionPayload,
  'requestForId' | 'requesterBackToBackIds' | 'membersInvolvedIds' | 'departmentCode' | 'sectionId'
> & {
  procPlanCurrency: string;
  procPlanValue: number;
  tenderMethod: TypeOf<typeof TenderMethod>;
};

export type PotentialDamage = {
  party: TypeOf<typeof PotentialDamageParty>;
  damages: PotentialDamageDetail[];
};

export type PotentialDamageDetail = {
  type: TypeOf<typeof PotentialDamageType> | string;
  potential: boolean;
  description?: string | null;
  value?: number | null;
  currency?: TypeOf<typeof Currency> | null;
};

export type BudgetOwnerDetail = {
  departmentId?: string;
  departmentName?: string;
  otherPSC?: string;
  value: number;
  percentage: number;

  skkApproval: boolean;
  skkJustification?: string;
  skkTechMOMFiles?: SharepointFile[];
  skkSupportFiles?: SharepointFile[];

  approvedWPNB: boolean;
  approvedAFE: boolean;
  workProgramAndBudget: {
    year: string;
    value: number;
    scheduleLines: {
      schedule: string;
      lines: string[];
    }[];
  }[];
  authorizationForExpenditure: {
    no: string;
    value: number;
    dateAt?: string;
    scheduleLines: {
      schedule: string;
      lines: string[];
    }[];
  }[];
  costCenter: { value: string }[];
};

export type BudgetOwnerPSC = {
  value: number;
  percentage: number;
  text: string;
  remarks: string;
  supportFiles?: SharepointFile;
};

export type OEReference = {
  references: string[];
  explanation: string;
  breakdownFiles?: SharepointFile;
  referenceFiles?: SharepointFile;
};

type PotentialDamageDetailData = {
  yesNo: boolean;
  description: string;
  currency?: TypeOf<typeof Currency>;
  value?: number;
  otherDescription?: string;
};

type PotentialDamageData = {
  death: PotentialDamageDetailData;
  injury: PotentialDamageDetailData;
  disease: PotentialDamageDetailData;
  physical: PotentialDamageDetailData;
  pollution: PotentialDamageDetailData;
};

export type UserInsurance = {
  previousSimiliarContract: boolean;
  contractNo: string;
  contractTitle: string;
  typeLimit: string;
  potentialDamage: {
    hcml: PotentialDamageData;
    contractor: PotentialDamageData;
    thirdParty: PotentialDamageData;
  };
  note: string;
};

export type LegalInsurance = {
  use: boolean;
  typeLimits: {
    type: string;
    use: boolean;
    limit: number;
    currency: TypeOf<typeof Currency>;
    remarks: string;
  }[];
  note: string;
};

export type TenderBidder = {
  potentialBidders: any[];
  daChecklist: string[];
  daIsJustification: boolean;
  daJustificationType: "Top Management" | "Authorized Officer";
  daJustificationsChecklist: { content: string; sub: string[] }[];
  daExplanation: string;
  daPotentialBidderJustification: string;
  daSupportingDocumentFiles?: SharepointFile[];
  smTypeofAuthorizedCandidate: string;
  smTypeofWorkChecklist: string[];
};

export type SupportingDocuments = {
  tmwsmDateAt?: string;
  tmwsmTitle: string;
  tmwsmFiles?: SharepointFile[];

  tmweFiles?: SharepointFile[];
  sowFiles?: SharepointFile[];
  aprFiles?: SharepointFile[];
  tecFiles?: SharepointFile[];
  sorFiles?: SharepointFile[];
  slaKpiFiles?: SharepointFile[];
};

export interface TitleContent {
  title: string;
  content: string;
}

export interface IdName {
  id: string;
  name: string;
}

export interface HSERiskCategory {
  areaOfRisk: string;
  items: string;
  remarks: string;
  consequenceHuman: TypeOf<typeof Consequence>;
  consequenceAsset: TypeOf<typeof Consequence>;
  consequenceEnvironment: TypeOf<typeof Consequence>;
  consequenceReputation: TypeOf<typeof Consequence>;
}

export interface InsuranceRiskCategory {
  type: string;
  useInsurance: boolean;
  limit: string;
  remarks: string;
}

export type FindRequisitionsFilter = BaseFindManyFilter & {
  id?: string;
  departmentId?: string;
  departmentIds?: string[];
  sectionId?: string;
  year?: number;
  title?: string;
  status?: TypeOf<typeof DocumentStatus>;
  commodity?: TypeOf<typeof Commodity>;
  tenderCode?: string;
  useSelect?: boolean;
  assigned?: boolean;
  assignedBTB?: boolean;
  userId?: string;
};

export type FindRequisitionByRbtbFilter = BaseFindManyFilter & {
  userId: string;
  year?: number;
  title?: string;
  tenderCode?: string;
};

export type FindUserAssignedFilter = {
  users: User[];
};

export type SaveRequisition = SaveEntity<Requisition>;
export type FindRequisition = FindManyEntity<Requisition, FindRequisitionsFilter>;
export type FindRequisitionByRbtb = FindManyEntity<Requisition, FindRequisitionByRbtbFilter>;
export type FindUserAssigned = ActionHandler<FindUserAssignedFilter, { user: User; count: number }[]>;
export type FindRequisitionByFromExcludeDepartment = FindManyEntity<
  Requisition,
  { userId: string; year?: number; departmentId?: string; page?: number; size?: number; title?: string; tenderCode?: string }
>;
export type DeleteRequisition = DeleteEntity<{ id: string }>;

export const validateRequisitionRequest = (pd: RequisitionPayload) => {
  //

  {
    if (!pd.commodity || !Commodity.some((type) => type === pd.commodity)) {
      throw new Error(`commodity must be one of : ${Commodity}`);
    }

    if (pd.commodity === "GOODS") {
      if (!pd.poDateDelivery || !pd.poDateIssuance) {
        throw new Error("for commodity GOODS, poDateDelivery and poDateIssuance must specified");
      }

      if (pd.poDateIssuance > pd.poDateDelivery) {
        throw new Error("poDateIssuance must be less than poDateDelivery");
      }
    }

    if (pd.commodity === "SERVICES") {
      if (!pd.contractDateStart || !pd.contractDateEnd) {
        throw new Error("for commodity SERVICES, contractDateStart and contractDateEnd must specified");
      }

      if (pd.contractDateStart > pd.contractDateEnd) {
        throw new Error("contractDateStart must be less than contractDateEnd");
      }
    }

    if (pd.procPlanDetailId === "" && !pd.isAdditionalProcPlan) {
      throw new Error(`additonal must provide procplanDetailId`);
    }
  }

  {
    if (!pd.currency || !Currency.some((type) => type === pd.currency)) {
      throw new Error(`currency must be one of : ${Currency}`);
    }
  }

  // if (pd.localContentLevel >= 0) {
  //   throw new Error("localContentLevel must be specified");
  // }

  if (!pd.title) {
    throw new Error("title must be specified");
  }

  {
    if (!pd.tenderMethod || !TenderMethod.some((type) => type === pd.tenderMethod)) {
      throw new Error(`tenderMethod must be one of : ${TenderMethod}`);
    }
  }

  //
};

export const validateEligibilityRequisitionAction = (
  status: TypeOf<typeof DocumentStatus>,
  userLogin: User,
  userIDs: string[],
  ag: ApprovalGroup | null,
  atg: ApprovalTemplateGroup | null
): EligibilityActionWithCanAssign => {
  //
  const eligibility: EligibilityActionWithCanAssign = new EligibilityActionWithCanAssign();

  if (!status || status === "DRAFT") {
    eligibility.canCreate = atg
      ? atg.approvalTemplates.some((at) => at.users?.some((u) => u.id === userLogin.id) || at.role === userLogin.position?.role)
      : false;
  }

  if (status === "DRAFT") {
    if (userLogin.department?.code !== "") {
      try {
        validateActionByID(userLogin.id, userIDs);
        eligibility.canSubmit = true;
      } catch {
        //
      }
    }
  }

  if (ag && status === "ON_REVIEW") {
    if (ag.approvals.length > 1) {
      // paralel
      for (const app of ag.approvals) {
        // check delegation
        let delegation = activeDelegation(app);
        if (delegation && delegation.user.id === userLogin.id) {
          eligibility.canApproveOrSendBack = !app.signer;
          break;
        }
        
        // type users
        if (app.users && app.users.some((u) => u.id === userLogin.id)) {
          eligibility.canApproveOrSendBack = !app.signer;
          break;
        }

        // type role
        if (app.currentUserInPosition && app.currentUserInPosition.id === userLogin.id) {
          eligibility.canApproveOrSendBack = !app.signer;
          break;
        }
      }
    } else {
      eligibility.canApproveOrSendBack = ag.approvals.some(app => {
        if (app.users && app.users.length > 0) {
          // type users
          return app.users?.some((u) => u.id === userLogin.id) || userLogin.position?.id === app.position?.id
        } else {
          // type role
          return app.currentUserInPosition?.id === userLogin.id || userLogin.position?.id === app.currentUserInPosition?.position?.id
        }
      });
      
      // check delegation
      let delegation = activeDelegation(ag.approvals[0]);
      if (delegation && delegation.user.id === userLogin.id) {
        eligibility.canApproveOrSendBack = !ag.approvals[0].signer;
      }
    }
  }

  if (!ag?.nextApprovalGroupId && eligibility.canApproveOrSendBack) {
    eligibility.canAssign = true;
  }

  eligibility.canAdminUpdate = isUserAdmin(userLogin.email!) && ["ON_REVIEW", "APPROVED"].includes(status);
  eligibility.canAdminRevertApproval = isUserAdmin(userLogin.email!) && status === "ON_REVIEW" && (ag?.sequence ?? 0) > 2;

  return eligibility;
};

export const formatValueRequisition = (currency: TypeOf<typeof Currency> | undefined, value: number | undefined) => {
  if (!currency || !value) {
    return 0;
  }
  return currency === "USD" ? formatNumber(Number(value) || 0) : currency === "IDR" ? formatNumber((Number(value) || 0) / 10_000) : 0;
};

export const getCcEmailRequisition = (rq: Requisition, userLogin: User | null): string[] => {
  //
  let delegationEmails: string[] = [];
  if (rq.approvalGroup && rq.approvalGroup.approvals.length > 0) {
    for (const app of rq.approvalGroup.approvals) {
      // type users
      if (app.users && app.users.length > 0) {
        for (const users of app.users) {
          if (users.delegation && users.delegation.user) {
            delegationEmails.push(users.delegation!.user!.email!);
          }
        }
      }
      else {
        // type role
        if (app.currentUserInPosition?.delegation) {
          delegationEmails.push(app.currentUserInPosition!.delegation!.user!.email!);
        }
      }
    }
  }

  let ccUsers: string[] = [
    rq.submitter?.email || "",
    rq.creator?.email || "",
    rq.requestFor?.email || "",
    ...(rq.requesterBackToBack?.map((x) => x.email || "") || []),
    ...delegationEmails
  ];

  // assigner user
  if (!rq.approvalGroup?.nextApprovalGroupId || rq.approvalGroup?.nextApprovalGroupId === null) {
    ccUsers.push(
      userLogin?.email || "",
      rq.assignedUser?.email || "",
      ...(rq.assignedBackToBack?.map((x) => x.email || "") || []),
    );
  }

  const ccEmails = [...new Set(ccUsers.filter((email) => email !== ""))];

  return ccEmails;
};

export const getEmailDataRequisition = (rq: Requisition, userLogin: User | null, subDocType?: TypeOf<typeof SubDocumentRequisition> | null): MailTableData[] => {
  //
  const title: string = subDocType && getSubDocumentWording(subDocType) ? `${rq.title} (${getSubDocumentWording(subDocType)})` : rq.title;

  const rqData: MailTableData[] = [
    {
      title: title,
      department: rq.department?.name!,
      section: rq.section?.name!,
      quantity: rq.localContentLevel,
      value: formatValueRequisition(rq.currency, rq.value),
      // value: rq.value && rq.currency === "USD" ? formatNumber(rq.value) : rq.value && rq.currency === "IDR" ? formatNumber(rq.value / 10_000) : 0, // USD
      docId: rq.id,
      subDocType: rq.approvalGroup?.approvals[0].subDocumentType,
    },
  ];

  return rqData;
};

export const setRequisitionAssignTenderCode = async (ctx: Context, rq: Requisition, findRequisition: FindRequisition) => {
  //
  const commodity = rq.commodity === "GOODS" ? "G" : rq.commodity === "SERVICES" ? "S" : "";

  let code = "";
  if (rq.currency === "USD") {
    if (rq.value <= 100_000) {
      code = "C";
    } else if (rq.value > 500_000) {
      code = "A";
    } else {
      code = "B";
    }
  } else if (rq.currency === "IDR") {
    if (rq.value <= 1_000_000_000) {
      code = "C";
    } else if (rq.value > 5_000_000_000) {
      code = "A";
    } else {
      code = "B";
    }
  }

  const year = new Date().getFullYear() % 100;
  const month = (new Date().getMonth() + 1).toString().padStart(2, "0");
  const [tenderCodes, count] = await findRequisition(ctx, { tenderCode: `${commodity}${code}${year}${month}` });

  if (count === 99) {
    throw new Error(`Tender code ${commodity}${code}${year}${month} is reach limit of 99`);
  }

  const incremented = Number(count + 1)
    .toString()
    .padStart(2, "0");

  return `${commodity}${code}${year}${month}${incremented}`;
};

export const sendApprovalEmailRequisition = (approvalGroups: ApprovalGroup, rq: Requisition, ccUsers: string[], userLogin: User) => {
  //
  for (const approval of approvalGroups.approvals) {
    if (approval.signer && approval.status === "DONE") {
      continue;
    }

    let approvalUsers: User[] = [];
    if (approval!.users && approval!.users?.length > 0) {
      approvalUsers = approval!.users;
    } else {
      approvalUsers = [approval!.currentUserInPosition!];
    }
  
    let mailData: MailTableData[] = getEmailDataRequisition(rq, userLogin);
    mailData = mailData.map(dat => ({
      ...dat,
      approvalId: approvalGroups.id,
      subDocType: approval.subDocumentType
    }));
  
    for (const approvalUser of approvalUsers) {
      const delegationUser = approvalUserActiveDelegation(approvalUser);
      if (delegationUser) {
        const usersWithDelegation = [approvalUser, delegationUser.user];
        for (const user of usersWithDelegation) {
          sendApprovalMail({
            sendToUserMail: user.email!,
            sendToUserName: user.name!,
            ccUserMail: ccUsers,
            delegation: {
              delegatorName: approvalUser.name!,
              delegateToName: delegationUser.user.name!,
              delegatorPositionName: approval.as!,
            }
          }, mailData, "REQUISITION");
        }
        
      }
      else {
        sendApprovalMail({
          sendToUserMail: approvalUser.email!,
          sendToUserName: approvalUser.name!,
          ccUserMail: ccUsers,
        }, mailData, "REQUISITION");
      }
    }
  }
}
