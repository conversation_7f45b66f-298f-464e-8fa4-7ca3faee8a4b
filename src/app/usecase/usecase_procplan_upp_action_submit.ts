import { Usecase } from "../../framework/core.js";
import { DeleteApprovalGroups, FindApprovalGroup, SaveApprovalGroups, getApproval, getApprovalList, moveApproval } from "../model/model_approval.js";
import { FindApprovalTemplateGroup } from "../model/model_approval_template.js";
import { FindCalendar } from "../model/model_calendar.js";
import { FindDelegation } from "../model/model_delegation.js";
import { SaveDocumentHistory } from "../model/model_document_history.js";
import { FindProcPlanDetail, FindProcPlanHeader, ProcPlanDetail, SaveProcPlanDetail, SaveProcPlanHeader, validateUPPErrorFields } from "../model/model_procplan.js";
import { FindUser, FindUserSupervisors, UserLogin } from "../model/model_user.js";
import { <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON>StringHandler, format<PERSON><PERSON><PERSON>, getEmptyValueCurrency, validateActionByID } from "../model/vo.js";
import { formatDateWithSecond, getDateOnly } from "../utility/helper.js";
import { MailTemplateTableData, sendMail } from "../utility/mailer.js";

class Gateways {
  findUser: FindUser;
  findProcPlanHeader: FindProcPlanHeader;
  findProcPlanDetail: FindProcPlanDetail;
  findApprovalGroup: FindApprovalGroup;
  findUserSupervisors: FindUserSupervisors;
  findApprovalTemplateGroup: FindApprovalTemplateGroup;
  findDelegation: FindDelegation;
  findCalendar: FindCalendar;
  saveProcPlanHeader: SaveProcPlanHeader;
  saveDocumentHistory: SaveDocumentHistory;
  saveApprovalGroups: SaveApprovalGroups;
  saveProcPlanDetail: SaveProcPlanDetail;
  deleteApprovalGroups: DeleteApprovalGroups;
  dateNow: DateNowHandler;
  randomString: RandomStringHandler;
}

export class Request {
  userLogin: UserLogin;
  headerId: string;
  comment: string;
  // newAPPHeaderId: string;
  // now: DateOrString;
}

export class Response { }

export const procplanUppActionSubmit: Usecase<Gateways, Request, Response> = {
  gateways: Gateways,
  request: Request,
  setup: (o) => async (ctx, req) => {
    //

    const now = await o.dateNow(ctx);
    const newAPPHeaderId = await o.randomString(ctx);

    const [pphs] = await o.findProcPlanHeader(ctx, { id: req.headerId, procPlanType: "UPP" });
    let pph = pphs.length > 0 ? pphs[0] : null;

    if (!pph) {
      throw new Error("procplan header not found");
    }

    if (pph.status !== "DRAFT") {
      throw new Error("proc plan must in DRAFT state");
    }

    const [ppds] = await o.findProcPlanDetail(ctx, { procPlanHeaderId: req.headerId });

    // const creatorIds = ppds.map((x) => x.creator?.id);
    // const requestForIds = ppds.map((x) => x.requestFor?.id);
    // const rbtbsId = ppds.flatMap((x) => x.requesterBackToBack.map((x) => x.id));

    const [sectionUsers] = await o.findUser(ctx, {
      sectionId: pph.submitter?.section!.id!,
    });

    validateActionByID(req.userLogin.id, [
      //
      ...(sectionUsers.map((x) => x.id) as string[]),
      ...(ppds.map((x) => x.creator?.id) as string[]),
      ...(ppds.map((x) => x.requestFor?.id) as string[]),
      ...(ppds.flatMap((x) => x.requesterBackToBack.map((x) => x.id)) as string[]),
    ],
      req.userLogin.section!.id,
      pph.section!.id
    );

    validateUPPErrorFields(ppds);

    // recreate approval group
    {
      // request for user di procplan detail pertama save
      // const ppdsByCreatedAt = ppds.sort((a, b) => a.createdAt.getTime() - b.createdAt.getTime())[0];
      // const ppdsByRoleStaff = ppds.filter((ppd) => ppd.requestFor?.position?.role === "STAFF")[0];
      // const requestForUser = ppdsByRoleStaff ? ppdsByRoleStaff.requestFor : ppdsByCreatedAt.requestFor;
      const requestForUser = ppds.find((x) => x.requestFor?.position?.role === "STAFF")?.requestFor ?? ppds.find((x) => x.requestFor?.position?.role === "HEAD")?.requestFor;

      pph.approvalGroup = null;
      await o.saveProcPlanHeader(ctx, pph);

      // const [recreatedPPH] = await o.findProcPlanHeader(ctx, { id: req.headerId, procPlanType: "UPP" });
      // pph = recreatedPPH[0];

      // user including supervisors
      const [listUser] = await o.findUserSupervisors(ctx, { positionId: requestForUser!.position!.id! });
      if (listUser.length === 0) {
        throw new Error(`user supervisor for UPP is not found`);
      }

      const description = `${pph.department!.name}-${pph.section!.name}-${pph?.count!}Qty`;

      // buat approval
      const approvals = await getApprovalList(ctx, description, o.findUser, o.findApprovalTemplateGroup, o.findDelegation, listUser, pph.id, "PROC_PLAN_UPP", pph.year);

      // rule hasNoApprover
      if (approvals.length === 3) {
        approvals[1].nextApprovalGroupId = null;
        approvals.pop();
      }

      await o.deleteApprovalGroups(ctx, { documentId: pph.id, documentType: "PROC_PLAN_UPP" });

      // simpan approval
      await o.saveApprovalGroups(ctx, approvals);

      // pointer nunjuk ke approval pertama
      pph.approvalGroup = approvals[0];
    }

    const approval = getApproval(pph.approvalGroup!, req.userLogin);
    if (approval) {
      approval.date = getDateOnly(now);
      approval.signer = req.userLogin;
      approval.status = "DONE";
    }

    const paralelAND = pph.approvalGroup ? pph.approvalGroup.approvals.every((x) => x.status === "DONE") : false;

    pph.submitter = req.userLogin;

    // set data for email notification
    if (
      req.userLogin.id !== (
        pph.approvalGroup.approvals[0].users ? pph.approvalGroup.approvals[0].users[0].id : pph.approvalGroup.approvals[0].currentUserInPosition?.id
      ) &&
      pph.approvalGroup && pph.approvalGroup.sequence! === 1
    ) {
      const approvalUser = pph.approvalGroup.approvals[0].currentUserInPosition ?? pph.approvalGroup.approvals[0].users![0];
      const ppdReminders: MailTemplateTableData[] = [{
        department: pph.department?.name!,
        section: pph.section?.name!,
        quantity: pph.count,
        value: pph.totalValueEstimation ? formatNumber(pph.totalValueEstimation.find((x) => x.currency === "USD")?.value! + (pph.totalValueEstimation.find((x) => x.currency === "IDR")?.value! / 10_000)) : 0, // USD
        url: "/procurement-plan/request/upp?section=" + pph.section?.id,
      }];

      sendMail({
        sendToUserMail: approvalUser.email!,
        sendToUserName: approvalUser.name!,
        submittedByUserName: pph.submitter.name!,
        mailSubject: "PRISA - Notification for Procurement Plan",
        mailTitle: "Notification for Procurement Plan",
      }, ppdReminders, "NOTIFICATION");
    }

    const autoApprove = await moveApproval(ctx, paralelAND, pph, o.findApprovalGroup, o.saveApprovalGroups, o.findCalendar);

    await o.saveProcPlanHeader(ctx, pph);

    await o.saveDocumentHistory(ctx, {
      documentId: pph.id!,
      documentType: "PROC_PLAN_UPP",
      comment: req.comment,
      date: getDateOnly(now),
      message: `UPP Submitted`,
      user: req.userLogin,
      id: `${pph.id}-${formatDateWithSecond(now)}`,
    });

    // set data for email reminder
    if (pph.approvalGroup && pph.approvalGroup.sequence! > 1) {
      const approvalUser = pph.approvalGroup.approvals[0].currentUserInPosition ?? pph.approvalGroup.approvals[0].users![0];
      const ppdReminders: MailTemplateTableData[] = [{
        department: pph.department?.name!,
        section: pph.section?.name!,
        quantity: pph.count,
        value: pph.totalValueEstimation ? formatNumber(pph.totalValueEstimation.find((x) => x.currency === "USD")?.value! + (pph.totalValueEstimation.find((x) => x.currency === "IDR")?.value! / 10_000)) : 0, // USD
        url: "/procurement-plan/request/upp?section=" + pph.section?.id,
      }];

      sendMail({
        sendToUserMail: approvalUser.email!,
        sendToUserName: approvalUser.name!,
        mailSubject: "PRISA - Approval for Procurement Plan",
        mailTitle: "Approval for Procurement Plan",
      }, ppdReminders, "APPROVAL");
    }


    if (autoApprove) {
      // ==============================================================================================================================
      // ==============================================================================================================================
      // ==============================================================================================================================
      // ==============================================================================================================================
      // ========================================== APP DIMULAI DISINI ================================================================
      // ==============================================================================================================================
      // ==============================================================================================================================
      // ==============================================================================================================================
      // ==============================================================================================================================

      // kalau masih ada next approval alias ini adalah bukan approver terakhir maka kita masih skip pembuatan APP
      if (pph.approvalGroup) {
        return {};
      }

      // const requestForUser = pphUPP.requestFor as User;
      // const rbtbs = pphUPP.requesterBackToBack;

      const [pphAPPs] = await o.findProcPlanHeader(ctx, {
        procPlanType: "APP",
        departmentIds: [req.userLogin.department?.id!],
        year: pph.year,
      });
      let pphAPP = pphAPPs.length > 0 ? pphAPPs[0] : null;

      if (!pphAPP) {
        pphAPP = {
          id: newAPPHeaderId,
          year: pph.year,
          procPlanType: "APP",
          status: "DRAFT",
          isSendBack: false,
          submittedDate: getDateOnly(now),
          count: 0,
          section: null,
          department: { id: req.userLogin.department?.id! },
          approvalGroup: null,
          totalValueEstimation: getEmptyValueCurrency(),
          submitter: req.userLogin,
          sections: [],
          fromProcPlanHeaderId: pph.id,
        };

        // user including supervisors
        const [listUser] = await o.findUserSupervisors(ctx, { positionId: req.userLogin.supervisorPositionId! });
        if (listUser.length === 0) {
          throw new Error(`user supervisor for UPP is not found`);
        }

        const description = `${pphAPP?.department?.name}-${pphAPP?.count!}Qty`;

        // approval list hanya dibuat sekali pada saat pertama kali dibuatkan APP header
        const approvals = await getApprovalList(ctx, description, o.findUser, o.findApprovalTemplateGroup, o.findDelegation, listUser, pphAPP.id, "PROC_PLAN_APP", pphAPP.year);

        await o.deleteApprovalGroups(ctx, { documentId: pphAPP.id });

        // simpan approval
        await o.saveApprovalGroups(ctx, approvals);

        // pointer nunjuk ke approval pertama
        pphAPP.approvalGroup = approvals[0];

        await o.saveProcPlanHeader(ctx, pphAPP);
      }

      // procplan header harus dalam DRAFT status
      if (pphAPP!.status !== "DRAFT") {
        throw new Error("proc plan must in DRAFT state");
      }

      const [ppdUPPs] = await o.findProcPlanDetail(ctx, { procPlanHeaderId: pph.id });
      if (ppdUPPs.length === 0) {
        throw new Error(`no procplan detail found in proc pplan header with id ${pph.id}`);
      }

      // let currentSequenceId = pphAPP!.nextProcPlanDetailSequenceId as number;

      await o.saveProcPlanDetail(
        ctx,
        ppdUPPs.map((x) => {
          //
          // pphAPP!.totalValueEstimation += x.valueEstimation;

          pphAPP!.totalValueEstimation.find((v) => v.currency === x.currency)!.value += Number(x.valueEstimation);

          return {
            ...x,
            procPlanHeader: pphAPP,
            procPlanType: "APP",
            id: `APP-${x.id.slice(4)}`,
            createdAt: now,
          } as ProcPlanDetail;
        })
      );

      pphAPP.count! += ppdUPPs.length;

      o.saveProcPlanHeader(ctx, pphAPP);
    }

    return {};
  },
};
