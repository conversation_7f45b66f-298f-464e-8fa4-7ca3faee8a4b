import { Usecase } from "../../framework/core.js";
import {
  EvaluationSubmission,
  FindOnePQTemplate,
  FindPQVendor,
  FindPQVendorFilter,
  PQSubmissionInfo,
  PrequalificationTemplate,
  PrequalificationVendor,
  VendorPhasePQSubmission,
} from "../model/model_prequalification.js";
import { UserLogin } from "../model/model_user.js";
import { Vendor } from "../model/model_vendor.js";

class Gateways {
  findPQVendor: FindPQVendor;
  findOnePQTemplate: FindOnePQTemplate;
}

export class Request extends FindPQVendorFilter {
  userLogin: UserLogin;
}

export class Response {
  // extends InputResponseWithCount<TransformPQVendor>
  header: Partial<PrequalificationTemplate>;
}

export const pqVendorGetAll: Usecase<Gateways, Request, Response> = {
  gateways: Gateways,
  request: Request,
  setup: (o) => async (ctx, req) => {
    if (!req.pqId) {
      throw new Error("pqId is required");
    }

    const pqTemplate = await o.findOnePQTemplate(ctx, { pqId: req.pqId });
    if (!pqTemplate) {
      throw new Error("pq template not found");
    }

    const header: Partial<PrequalificationTemplate> = {
      id: pqTemplate.id,
      tenderCode: pqTemplate.tenderCode,
      title: pqTemplate.title,
      generalScopeOfWorks: pqTemplate.generalScopeOfWorks,
      businessClass: pqTemplate.businessClass,
      businessLicenses: pqTemplate.businessLicenses,
      // businessFields: pqTemplate.businessFields,
      localContentLevel: pqTemplate.localContentLevel,
      highRiskCategory: pqTemplate.highRiskCategory,
      currentPhase: pqTemplate.currentPhase,
      pqRegistrationDate: pqTemplate.pqRegistrationDate,
      announcementDate: pqTemplate.announcementDate,
      prequalificationType: pqTemplate.prequalificationType,
      pqMeetingDate: pqTemplate.pqMeetingDate,
      pqMeeting: pqTemplate.pqMeeting,
      assignedUser: pqTemplate.assignedUser,
      requestedBackToBacks: pqTemplate.requestedBackToBacks,
      pqSubmissionDate: pqTemplate.pqSubmissionDate,
    };

    if (!req.currentPhase) {
      throw new Error("currentPhase is required");
    }

    var [items, count] = await o.findPQVendor(ctx, {
      pqId: req.pqId,
      currentPhase: req.currentPhase,
    });

    var results: SubmissionResult | VendorPQResult[] = [];

    if (req.currentPhase === "EVALUATION") {
      var vendorSubmission1: VendorSubmission[] = [];
      var vendorSubmission2: VendorSubmission[] = [];
      var vendorSubmission3: VendorSubmission[] = [];

      items.map((item) => {
        if (item.phaseSubmission?.submission1) {
          vendorSubmission1.push({
            id: item.id,
            civdVendorId: item.civdVendorId,
            entity: item.phaseSubmission.submission1.entity,
            companyEntities: item.phaseSubmission.submission1.companyEntities,
            resultSummary: item.phaseSubmission.submission1.resultSummary,
            isPublish: item.phaseSubmission.submission1.isPublish,
            submitDate: item.phaseSubmission.submission1.submitDate,
            evaluationDate: item.phaseSubmission.submission1.evaluationDate,
            status: item.status,
          });
        } else if (item.phaseSubmission?.submission2) {
          vendorSubmission2.push({
            id: item.id,
            civdVendorId: item.civdVendorId,
            entity: item.phaseSubmission.submission2.entity,
            companyEntities: item.phaseSubmission.submission2.companyEntities,
            resultSummary: item.phaseSubmission.submission2.resultSummary,
            isPublish: item.phaseSubmission.submission2.isPublish,
            submitDate: item.phaseSubmission.submission2.submitDate,
            evaluationDate: item.phaseSubmission.submission2.evaluationDate,
            status: item.status,
          });
        } else if (item.phaseSubmission?.submission3) {
          vendorSubmission3.push({
            id: item.id,
            civdVendorId: item.civdVendorId,
            entity: item.phaseSubmission.submission3.entity,
            companyEntities: item.phaseSubmission.submission3.companyEntities,
            resultSummary: item.phaseSubmission.submission3.resultSummary,
            isPublish: item.phaseSubmission.submission3.isPublish,
            submitDate: item.phaseSubmission.submission3.submitDate,
            evaluationDate: item.phaseSubmission.submission3.evaluationDate,
            status: item.status,
          });
        }
      });

      results = {
        submission1: {
          response: pqTemplate.phasesEvaluation?.submission1?.response,
          dueDate: pqTemplate.phasesEvaluation?.submission1?.dueDate,
          items: vendorSubmission1,
          approval: pqTemplate.phasesEvaluation?.submission1?.approval,
        },
        submission2: {
          response: pqTemplate.phasesEvaluation?.submission2?.response,
          dueDate: pqTemplate.phasesEvaluation?.submission2?.dueDate,
          items: vendorSubmission2,
          approval: pqTemplate.phasesEvaluation?.submission2?.approval,
        },
        submission3: {
          response: pqTemplate.phasesEvaluation?.submission3?.response,
          dueDate: pqTemplate.phasesEvaluation?.submission3?.dueDate,
          items: vendorSubmission3,
          approval: pqTemplate.phasesEvaluation?.submission3?.approval,
        },
      };
    } else if (req.currentPhase === "REGISTRATION") {
      results = items.map((item) => {
        return {
          id: item.id,
          civdVendorId: item.civdVendorId,
          vendor: {
            civdVendorId: item.vendor!.civdVendorId,
            companyType: item.vendor!.companyType,
            name: item.vendor!.name,
            npwp: item.vendor!.npwp,
            email: item.vendor!.email,
          },
          result: item.phaseRegistration?.resultSummary,
          registrationDate: item.phaseRegistration?.submitDate || null,
          evaluationDate: item.phaseRegistration?.evaluationDate || null,
          status: item.status,
        };
      });
    } else if (req.currentPhase === "CLARIFICATION") {
      results = items.map((item) => {
        return {
          id: item.id,
          civdVendorId: item.civdVendorId,
          result: item.phaseSubmission ? getLastVendorSubmission(item.phaseSubmission) : null,
          registrationDate: item.phaseClarification?.clarificationDocuments?.submitDate || null,
          evaluationDate: item.phaseClarification?.clarificationDocuments?.evaluationDate || null,
          status: item.status,
        };
      });
    } else {
      results = [];
      count = 0;
    }

    return { header: header, items: results, count };
  },
};

const getLastVendorSubmission = (pqVendorSubmission: VendorPhasePQSubmission) => {
  if (pqVendorSubmission && pqVendorSubmission.submission3) {
    return pqVendorSubmission.submission3;
  } else if (pqVendorSubmission && pqVendorSubmission.submission2) {
    return pqVendorSubmission.submission2;
  } else if (pqVendorSubmission && pqVendorSubmission.submission1) {
    return pqVendorSubmission.submission1;
  } else {
    return null;
  }
};

type VendorPQResult = Partial<PrequalificationVendor>;

type SubmissionResult = {
  submission1: Partial<EvaluationSubmission> & { items: VendorSubmission[] };
  submission2: Partial<EvaluationSubmission> & { items: VendorSubmission[] };
  submission3: Partial<EvaluationSubmission> & { items: VendorSubmission[] };
};

type VendorSubmission = Partial<PQSubmissionInfo> & {
  id: string;
  civdVendorId: number;
  status: string;
};

// TODO:: clarification list = last submission that "FAIL" and vendor submit for clarification
// TODO:: clarification list tab only submit response with approval
// TODO:: clarification result only submit vendor PQ clarifications documents

// TODO:: clarification response if "result" make all vendors "PASS"
// TODO:: clarification response if "meeting" must make MoM for meeting as vendor PQ response and can edit vendor clarifications documents
