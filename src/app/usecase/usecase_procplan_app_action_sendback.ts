import { Usecase } from "../../framework/core.js";
import { FindApprovalGroup, SaveApprovalGroups, validateApprovalAction } from "../model/model_approval.js";
import { FindDelegation } from "../model/model_delegation.js";
import { SaveDocumentHistory } from "../model/model_document_history.js";
import { FindProcPlanHeader, SaveProcPlanHeader } from "../model/model_procplan.js";
import { UserLogin } from "../model/model_user.js";
import { DateNowHandler, formatNumber } from "../model/vo.js";
import { formatDateWithSecond, getDateOnly } from "../utility/helper.js";
import { MailTemplateTableData, sendMail } from "../utility/mailer.js";

class Gateways {
  findProcPlanHeader: FindProcPlanHeader;
  findApprovalGroup: FindApprovalGroup;
  findDelegation: FindDelegation;
  saveProcPlanHeader: SaveProcPlanHeader;
  saveDocumentHistory: SaveDocumentHistory;
  saveApprovalGroups: SaveApprovalGroups;
  dateNow: DateNowHandler;
}

export class Request {
  userLogin: UserLogin;
  headerId: string;
  comment: string;
  // now: DateOrString;
}

export class Response { }

export const procplanAppActionSendback: Usecase<Gateways, Request, Response> = {
  gateways: Gateways,
  request: Request,
  setup: (o) => async (ctx, req) => {
    //

    const now = await o.dateNow(ctx);

    const [pphs] = await o.findProcPlanHeader(ctx, { id: req.headerId, procPlanType: "APP" });
    let pph = pphs.length > 0 ? pphs[0] : null;

    // let pph = await getLastProcplanHeader(ctx, findProcPlanHeader, req.departmentId, null, req.year, "APP");

    if (!pph) {
      throw new Error("procplan header not found");
    }

    if (pph.status !== "ON_REVIEW") {
      throw new Error("proc plan must in ON_REVIEW state");
    }

    const [delegations] = await o.findDelegation(ctx, { delegateToUserId: req.userLogin.id, type: "DELEGATION" });
    validateApprovalAction(req.userLogin, pph.approvalGroup, null, delegations[0]);

    // RESET ulang semua approval
    {
      //====
      const [approvals] = await o.findApprovalGroup(ctx, { documentId: pph.id });
      approvals.forEach((apps, i) => {
        apps.approvals.forEach((app) => {
          app.date = null;
          app.signer = null;
          app.status = i === 0 ? "PROCESSING" : "NOT_STARTED";
        });
        apps.status = i === 0 ? "PROCESSING" : "NOT_STARTED";
      });

      await o.saveApprovalGroups(ctx, approvals);

      pph.status = "DRAFT";
      pph.isSendBack = true;
      pph.approvalGroup = approvals[0];

      await o.saveProcPlanHeader(ctx, pph);
      //====
    }

    if (req.comment === "") {
      throw new Error("comment is required");
    }

    await o.saveDocumentHistory(ctx, {
      documentId: pph.id!,
      documentType: "PROC_PLAN_APP",
      comment: req.comment,
      date: getDateOnly(now),
      message: `APP Sent Back`,
      user: req.userLogin,
      id: `${pph.id}-${formatDateWithSecond(now)}`,
    });

    const sectionsName = pph.sections.map((section) => section.name).join(", <br/>");

    // send mail
    const ppdReminders: MailTemplateTableData[] = [{
      department: pph.department?.name!,
      section: sectionsName,
      quantity: pph.count,
      value: pph.totalValueEstimation ? formatNumber(pph.totalValueEstimation.find((x) => x.currency === "USD")?.value! + (pph.totalValueEstimation.find((x) => x.currency === "IDR")?.value! / 10_000)) : 0, // USD
      url: "/procurement-plan/request/app?department=" + pph.department?.id,
    }];

    sendMail({
      sendToUserMail: pph.submitter!.email!,
      sendToUserName: pph.submitter!.name!,
      mailSubject: "PRISA - Procurement Plan Send Back",
      mailTitle: "Procurement Plan Send Back",
    }, ppdReminders, "SENDBACK");

    return {};
  },
};
