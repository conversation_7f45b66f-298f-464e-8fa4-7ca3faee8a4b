import { Usecase } from "../../framework/core.js";
import { calculatePQWavPassingLevel, FindOnePQTemplate, PQBusinessField, PQBusinessLicense, PQOtherInformations, SavePQTemplate } from "../model/model_prequalification.js";
import { FindVendor, Vendor } from "../model/model_vendor.js";
import { Business, BusinessClass, CompanyStatus, HighestExperienceScore, PrequalificationType, TypeOf } from "../model/vo.js";

class Gateways {
  findOnePQTemplate: FindOnePQTemplate;
  savePQTemplate: SavePQTemplate;
  findVendor: FindVendor;
}

export class Request {
  pqId: string;
  payload: {
    pqMeeting: boolean;
    pqMeetingDate: Date | null;
    pqSubmissionDate: Date | null;
    justification: string;
    otherInformations?: PQOtherInformations[];
    domicile: string;
    companyStatus: TypeOf<typeof CompanyStatus>;
    businessClass: TypeOf<typeof BusinessClass>;
    businessType: TypeOf<typeof Business>;
    businessLicense: string;
    businessFields: PQBusinessField[];
    convertionRateUSDToIDR: number;
    basicCapability: number;
    highestExperienceScore?: TypeOf<typeof HighestExperienceScore>; // 1/3 atau 1/5
    financialDueDiligence: boolean;
    prequalificationType: TypeOf<typeof PrequalificationType>;
    invitedVendors?: number[] | [];
    businessLicenses?: PQBusinessLicense[];
  };
}

export class Response { }

export const pqRequirementUpdate: Usecase<Gateways, Request, Response> = {
  gateways: Gateways,
  request: Request,
  setup: (o) => async (ctx, req) => {
    // TODO stil can update if it is DRAFT

    const pqTemplate = await o.findOnePQTemplate(ctx, req);

    if (pqTemplate?.currentPhase !== "REQUIREMENT") {
      throw new Error("Current Phase is not REQUIREMENT");
    }

    // TODO refactor it later
    {
      if (!pqTemplate) {
        throw new Error(`PQ Vendor with id ${req.pqId} is not found`);
      }

      if (!Business.some((type) => type === req.payload.businessType)) {
        throw new Error(`BusinessType must one of ${Business}`);
      }

      // if (req.payload.businessLicense === "") {
      //   throw new Error("businessLicence must no empty");
      // }

      if (req.payload.prequalificationType === "INVITATION") {
        // if (req.payload.invitedVendors?.length === 0) {
        //   throw new Error("vendorIds at least one");
        // }
        // const [vendorObjs] = await o.findVendor(ctx, { ids: req.payload.invitedVendors?.map((vendor) => vendor.id) });
        // if (!req.payload.invitedVendors?.every((item) => vendorObjs.map((x) => x.id).includes(item.id))) {
        //   throw new Error("some vendorIds is not found");
        // }
      }
    }

    // calculate Weighted Average Value for Passing Level
    if (req.payload.financialDueDiligence) {
      pqTemplate.wavPassingLevel = calculatePQWavPassingLevel(pqTemplate.currency, pqTemplate.ownerEstimateValue);
    }

    const newUpdatedPQ = {
      //
      ...pqTemplate,
      ...req.payload,
    };

    await o.savePQTemplate(ctx, newUpdatedPQ);

    return {};
  },
};