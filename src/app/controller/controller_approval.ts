import { HTTPDataItem } from "../../framework/data_http.js";
import { ContractTypeEngagement, ContractTypePayment } from "../model/vo.js";
import { approvalStatus, commodity, documentType, queryPageAndSize, userLogin } from "./_reused_properties.js";

export const controllerApproval: HTTPDataItem[] = [
  //
  {
    method: "post",
    path: "/approvaladminrevert",
    usecase: "approvalAdminRevert",
    tag: "approval",
    security: "bearerAuth",
    local: { userLogin },
    body: {
      documentType: documentType,
      documentId: { type: "string" },
    },
  },
  {
    method: "post",
    path: "/approvalresendemail",
    usecase: "approvalResendEmail",
    tag: "approval",
    security: "bearerAuth",
    local: { userLogin },
    body: {
      documentType: documentType,
      documentId: { type: "string" },
      resendGeneralApprovalMail: { type: "boolean" },
    },
  },
  {
    method: "post",
    path: "/approvalflow",
    usecase: "approvalFlow",
    tag: "approval",
    security: "bearerAuth",
    body: {
      documentType: documentType,
      // contractType: contractType,
      // isUseInsurance: { type: "boolean" },
      contractTypeEngagement: { type: "string", enum: ContractTypeEngagement },
      contractTypePayment: { type: "string", enum: ContractTypePayment },
      commodity: commodity,
      value: { type: "number" }, // USD
      positionId: { type: "string" },
      departmentCode: { type: "string" },
      departmentId: { type: "string" },
      sectionId: { type: "string" },
      budgetOwners: {
        type: "array",
        items: { type: "string" },
      },
      isAdditionalProcPlan: { type: "boolean" },
      procPlanValue: { type: "number" },
    },
    responseAsTable: true,
  },
  {
    method: "get",
    path: "/approval",
    usecase: "approvalGetAll",
    tag: "approval",
    security: "bearerAuth",
    query: {
      documentType: documentType,
      status: approvalStatus,
      documentId: { type: "string" },
      userId: { type: "string" },
      positionId: { type: "string" },
    },
    responseAsTable: true,
  },
  {
    method: "get",
    path: "/approvalhistory",
    usecase: "documentHistoryGetAll",
    tag: "approval",
    security: "bearerAuth",
    query: {
      documentId: { type: "string" },
      year: { type: "date" },
    },
    responseAsTable: true,
    response: {
      200: {
        content: {
          date: { type: "string" },
          message: { type: "string" },
          comment: { type: "string" },
        },
      },
    },
  },
  {
    method: "get",
    path: "/approvalprocessing",
    usecase: "approvalProcessing",
    tag: "approval",
    security: "bearerAuth",
    responseAsTable: true,
    local: { userLogin },
    query: {
      ...queryPageAndSize,
      relatedUserApproval: { type: "boolean" },
      delegationUserApproval: { type: "boolean" },
    }
  },
];
