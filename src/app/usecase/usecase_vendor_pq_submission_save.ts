import { text } from "stream/consumers";
import { Usecase } from "../../framework/core.js";
import {
  CompanyEntity,
  FindOnePQTemplate,
  FindOnePQVendor,
  PQOtherDocuments,
  PQSubmissionInfo,
  ResultRemarks,
  SavePQVendor,
  SummaryExperience,
} from "../model/model_prequalification.js";
import { DateNowHandler, deskripsiBuktiStatusPDN, deskripsiDokumenDomisili, dokumenK3LL, Files, SharepointFile, TypeOf } from "../model/vo.js";
import { getDateOnly } from "../utility/helper.js";
import { CIVDVendor, FindOneVendorCIVD, vendorCIVDLogin } from "../model/model_civd_vendor.js";

class Gateways {
  findOnePQVendor: FindOnePQVendor;
  savePQVendor: SavePQVendor;
  findOneVendorCIVD: FindOneVendorCIVD;
  findOnePQTemplate: FindOnePQTemplate;
  dateNow: DateNowHandler;
}

export class Request {
  vendorCIVDLogin: vendorCIVDLogin;
  pqId: string;
  submissionPhase: number | 1 | 2 | 3;
  submissionPayload: {
    entity: "CONSORTIUM" | "SINGLE";
    companyEntities: CompanyEntity[]; // general information, bila entity = single hanya ada 1 data perusahaan ; bila entity = consortium, pemuka harus ada 1 dan tidak boleh lebih dari 1
    suratPernyataanPQ: SharepointFile;
    suratKuasaPernyataanPQ?: SharepointFile;
    suratSPDA: SharepointFile;
    dokumenBuktiStatusPDN: SharepointFile;
    dokumenBuktiStatusPDNDeskripsi: TypeOf<typeof deskripsiBuktiStatusPDN>;
    dokumenDomisili: SharepointFile;
    dokumenDomisiliDeskripsi: TypeOf<typeof deskripsiDokumenDomisili>;
    suratIzinUsaha: SharepointFile;
    sertifikatTKDN: SharepointFile;
    suratPerjanjianKonsorsium?: SharepointFile;
    dokumenK3LL: SharepointFile;
    dokumenK3LLDeskripsi: TypeOf<typeof dokumenK3LL>;
    summaryExperiences: SummaryExperience[];
    dokumenEvaluasiKemampuanFinansial: SharepointFile;
    dokumenLaporanKeuangan: SharepointFile;
    otherDocuments?: PQOtherDocuments[];
  };
}

export class Response {}

export const vendorPQSubmissionSave: Usecase<Gateways, Request, Response> = {
  gateways: Gateways,
  request: Request,
  setup: (o) => async (ctx, req) => {
    const now = await o.dateNow(ctx);

    const vendor = await o.findOneVendorCIVD(ctx, req.vendorCIVDLogin.civdVendorId);
    if (!vendor) {
      throw new Error("vendor not found");
    }

    const pqTemplate = await o.findOnePQTemplate(ctx, { pqId: req.pqId });
    if (!pqTemplate) {
      throw new Error("pq not found");
    }

    if (!pqTemplate.pqRegistrationDate) {
      throw new Error("pq registration date not found");
    }

    // validate pq registration due date
    if (getDateOnly(now) > pqTemplate.pqRegistrationDate) {
      // throw new Error("pq registration date is expired");
    }

    const pqVendor = await o.findOnePQVendor(ctx, { pqId: req.pqId, civdVendorId: req.vendorCIVDLogin.civdVendorId });
    if (!pqVendor || !pqVendor.phaseRegistration) {
      throw new Error("pq vendor not found");
    }

    if (pqVendor.phaseRegistration.resultSummary !== "PASS") {
      // throw new Error("pq vendor does not PASS the Registration phase");
    }

    if (pqVendor.status !== "REGISTRATION_SUBMIT") {
      throw new Error("pq submission already submitted");
    }

    if (pqTemplate.commodity === "GOODS" && pqTemplate.localContentLevel! >= 10 && !req.submissionPayload.sertifikatTKDN) {
      throw new Error("sertifikat TKDN is required for commodity 'GOODS'");
    }

    const nationalOEValue = 1_000_000;
    if (pqTemplate.ownerEstimateValue < nationalOEValue && !req.submissionPayload.dokumenDomisili) {
      throw new Error("dokumen domisili is required for national OE");
    }

    const otherDocuments: (PQOtherDocuments & ResultRemarks)[] = req.submissionPayload.otherDocuments
      ? req.submissionPayload.otherDocuments.map((otherDocuments) => ({
          text: otherDocuments.text,
          show: otherDocuments.show,
          files: otherDocuments.files ?? null,
          result: "UNDECIDED",
        }))
      : [];

    let phaseSubmissionPayload: PQSubmissionInfo = {
      entity: req.submissionPayload.entity,
      companyEntities: req.submissionPayload.companyEntities, // general information, bila entity = single hanya ada 1 data perusahaan ; bila entity = consortium, pemuka harus ada 1 dan tidak boleh lebih dari 1

      suratPernyataanPQ: { files: req.submissionPayload.suratPernyataanPQ, result: "UNDECIDED" },
      suratKuasaPernyataanPQ: req.submissionPayload.suratKuasaPernyataanPQ
        ? { files: req.submissionPayload.suratKuasaPernyataanPQ, result: "UNDECIDED" }
        : undefined,
      suratSPDA: { files: req.submissionPayload.suratSPDA, result: "UNDECIDED" },
      dokumenBuktiStatusPDN: {
        files: req.submissionPayload.dokumenBuktiStatusPDN,
        description: req.submissionPayload.dokumenBuktiStatusPDNDeskripsi,
        result: "UNDECIDED",
      },
      dokumenDomisili: { files: req.submissionPayload.dokumenDomisili, description: req.submissionPayload.dokumenDomisiliDeskripsi, result: "UNDECIDED" },
      suratIzinUsaha: { files: req.submissionPayload.suratIzinUsaha, result: "UNDECIDED" },
      sertifikatTKDN: { files: req.submissionPayload.sertifikatTKDN, result: "UNDECIDED" },

      // if entity = consortium
      suratPerjanjianKonsorsium: req.submissionPayload.suratPerjanjianKonsorsium
        ? { files: req.submissionPayload.suratPerjanjianKonsorsium, result: "UNDECIDED" }
        : undefined,

      dokumenK3LL: { files: req.submissionPayload.dokumenK3LL, description: req.submissionPayload.dokumenK3LLDeskripsi, result: "UNDECIDED" },
      summaryExperiences: { experiences: req.submissionPayload.summaryExperiences, result: "UNDECIDED" },

      dokumenEvaluasiKemampuanFinansial: { files: req.submissionPayload.dokumenEvaluasiKemampuanFinansial, result: "UNDECIDED" },
      dokumenLaporanKeuangan: { files: req.submissionPayload.dokumenLaporanKeuangan, result: "UNDECIDED" },

      otherDocuments: otherDocuments,

      resultSummary: "UNDECIDED",
      submitDate: getDateOnly(now),
      evaluationDate: null,
      isPublish: false,
    };

    if (!pqVendor.phaseSubmission) {
      pqVendor.phaseSubmission = {
        submission1: null,
        submission2: null,
        submission3: null,
      };
    }

    if (req.submissionPhase == 1) {
      pqVendor.phaseSubmission.submission1 = phaseSubmissionPayload;
    } else if (req.submissionPhase == 2) {
      pqVendor.phaseSubmission.submission2 = phaseSubmissionPayload;
    } else if (req.submissionPhase == 3) {
      pqVendor.phaseSubmission.submission3 = phaseSubmissionPayload;
    } else {
      throw new Error("invalid submission phase");
    }

    // TODO:: check status with k3s update

    await o.savePQVendor(ctx, pqVendor);

    return { req: pqVendor.phaseSubmission };
  },
};
