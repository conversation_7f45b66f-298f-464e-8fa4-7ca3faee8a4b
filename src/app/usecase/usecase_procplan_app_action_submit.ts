import { Usecase } from "../../framework/core.js";
import { DeleteApprovalGroups, FindApprovalGroup, SaveApprovalGroups, getApproval, getApprovalList, moveApproval, validateApprovalAction } from "../model/model_approval.js";
import { FindApprovalTemplateGroup } from "../model/model_approval_template.js";
import { FindCalendar } from "../model/model_calendar.js";
import { FindDelegation } from "../model/model_delegation.js";
import { SaveDocumentHistory } from "../model/model_document_history.js";
import { FindProcPlanDetail, FindProcPlanHeader, SaveProcPlanHeader, validateUPPErrorFields } from "../model/model_procplan.js";
import { FindUser, FindUserSupervisors, UserLogin } from "../model/model_user.js";
import { DateNowHandler, formatNumber } from "../model/vo.js";
import { formatDateWithSecond, getDateOnly } from "../utility/helper.js";
import { MailTemplateTableData, sendMail } from "../utility/mailer.js";

class Gateways {
  findProcPlanHeader: FindProcPlanHeader;
  findProcPlanDetail: FindProcPlanDetail;
  findApprovalGroup: FindApprovalGroup;
  findApprovalTemplateGroup: FindApprovalTemplateGroup;
  findUserSupervisors: FindUserSupervisors;
  findUser: FindUser;
  findDelegation: FindDelegation;
  deleteApprovalGroups: DeleteApprovalGroups;
  saveProcPlanHeader: SaveProcPlanHeader;
  saveDocumentHistory: SaveDocumentHistory;
  saveApprovalGroups: SaveApprovalGroups;
  findCalendar: FindCalendar;
  dateNow: DateNowHandler;
}

export class Request {
  userLogin: UserLogin;
  headerId: string;
  comment: string;
  // now: DateOrString;
}

export class Response { }

export const procplanAppActionSubmit: Usecase<Gateways, Request, Response> = {
  gateways: Gateways,
  request: Request,
  setup: (o) => async (ctx, req) => {
    //

    const now = await o.dateNow(ctx);

    const [pphs] = await o.findProcPlanHeader(ctx, { id: req.headerId, procPlanType: "APP" });
    let pph = pphs.length > 0 ? pphs[0] : null;

    // let pph = await getLastProcplanHeader(ctx, findProcPlanHeader, req.departmentId, null, req.year, "APP");

    if (!pph) {
      throw new Error("procplan header not found");
    }

    if (pph.status !== "DRAFT") {
      throw new Error("proc plan must in DRAFT state");
    }

    const [atg] = await o.findApprovalTemplateGroup(ctx, { documentType: "PROC_PLAN_APP" });
    validateApprovalAction(req.userLogin, pph.approvalGroup, atg[0]);

    const [ppds] = await o.findProcPlanDetail(ctx, { procPlanHeaderId: pph.id!, procPlanType: "APP" });

    validateUPPErrorFields(ppds);

    // create approval group
    {
      const [usersDepartment] = await o.findUser(ctx, { departmentId: pph.department?.id });
      if (usersDepartment.length === 0) {
        throw new Error(`user department for APP is not found`);
      }

      const userIsSection = usersDepartment.filter((user) => (user.section?.isSection));

      const userDepartment = userIsSection.find((user) => (user.position?.role === "STAFF" || user.position?.role === "HEAD"));
      if (!userDepartment) {
        throw new Error(`user with role STAFF or HEAD department for APP is not found`);
      }

      // user including supervisors
      const [listUser] = await o.findUserSupervisors(ctx, { positionId: userDepartment.supervisorPositionId! });
      if (listUser.length === 0) {
        throw new Error(`user supervisor for APP is not found`);
      }

      const description = `${userDepartment.department?.name}-${ppds.length}Qty`;

      // approval list hanya dibuat sekali pada saat pertama kali dibuatkan APP header
      const approvals = await getApprovalList(ctx, description, o.findUser, o.findApprovalTemplateGroup, o.findDelegation, listUser, pph.id, "PROC_PLAN_APP", pph.year);

      pph.approvalGroup = null;
      await o.saveProcPlanHeader(ctx, pph);

      await o.deleteApprovalGroups(ctx, { documentId: pph.id });

      // simpan approval
      await o.saveApprovalGroups(ctx, approvals);

      // pointer nunjuk ke approval pertama
      pph.approvalGroup = approvals[0];

      await o.saveProcPlanHeader(ctx, pph);
    }

    pph.submitter = req.userLogin;

    // pph.approval!.date = req.now;
    // pph.approval!.signer = req.userLogin;
    // pph.approval!.status = "DONE";

    const approval = getApproval(pph.approvalGroup!, req.userLogin);
    if (approval) {
      approval.date = getDateOnly(now);
      approval.signer = req.userLogin;
      approval.status = "DONE";
    }

    const paralelAND = pph.approvalGroup ? pph.approvalGroup.approvals.every((x) => x.status === "DONE") : false;

    // if (pph.approvalGroup!.nextApprovalGroupId && paralelAND) {
    //   //

    //   const nextApproval = await findNextApprovalObject(ctx, findApproval, pph.approvalGroup!.nextApprovalGroupId!);

    //   nextApproval.status = "PROCESSING";

    //   await saveApprovals(ctx, [pph.approvalGroup!, nextApproval]);

    //   pph.status = "ON_REVIEW";
    //   pph.isSendBack = false;
    //   pph.approvalGroup = nextApproval;
    //   await saveProcPlanHeader(ctx, pph);

    //   //
    // } else {
    //   //
    //   await saveApprovals(ctx, [pph.approvalGroup!]);

    //   // auto approve!
    //   pph.status = "APPROVED";
    //   pph.isSendBack = false;
    //   pph.approvalGroup = null;
    //   await saveProcPlanHeader(ctx, pph);
    //   //
    // }

    await moveApproval(ctx, paralelAND, pph, o.findApprovalGroup, o.saveApprovalGroups, o.findCalendar);

    await o.saveProcPlanHeader(ctx, pph);

    await o.saveDocumentHistory(ctx, {
      documentId: pph.id!,
      documentType: "PROC_PLAN_APP",
      comment: req.comment,
      date: getDateOnly(now),
      message: `APP Submitted`,
      user: req.userLogin,
      id: `${pph.id}-${formatDateWithSecond(now)}`,
    });

    // get user approval group
    if (pph.approvalGroup && pph.approvalGroup.sequence! > 1) {
      // set data for email reminder
      const approvalUser = pph.approvalGroup.approvals[0].currentUserInPosition ?? pph.approvalGroup.approvals[0].users![0];
      const sectionsName = pph.sections.map((section) => section.name).join(", <br/>");

      const ppdReminders: MailTemplateTableData[] = [{
        department: pph.department?.name!,
        section: sectionsName,
        quantity: ppds.length,
        value: pph.totalValueEstimation ? formatNumber(pph.totalValueEstimation.find((x) => x.currency === "USD")?.value! + (pph.totalValueEstimation.find((x) => x.currency === "IDR")?.value! / 10_000)) : 0, // USD
        url: "/procurement-plan/request/app?department=" + pph.department?.id,
      }];

      sendMail({
        sendToUserMail: approvalUser.email!,
        sendToUserName: approvalUser.name!,
        mailSubject: "PRISA - Approval for Procurement Plan",
        mailTitle: "Approval for Procurement Plan",
      }, ppdReminders, "APPROVAL");
    }

    return {};
  },
};
