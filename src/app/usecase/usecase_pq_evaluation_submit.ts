import { Context, Usecase } from "../../framework/core.js";
import { FindApprovalGroup, getApproval, moveApproval, SaveApprovalGroups } from "../model/model_approval.js";
import { FindCalendar } from "../model/model_calendar.js";
import { SaveDocumentHistory } from "../model/model_document_history.js";
import { EvaluationSubmission, FindOnePQTemplate, FindPQVendor, SavePQTemplate, SavePQVendor } from "../model/model_prequalification.js";
import { UserLogin } from "../model/model_user.js";
import { DateNowHandler, DocumentTemplate, TypeOf } from "../model/vo.js";
import { formatDateWithSecond, getDateOnly } from "../utility/helper.js";

class Gateways {
  findOnePQTemplate: FindOnePQTemplate;
  findPQVendor: FindPQVendor;
  savePQTemplate: SavePQTemplate;
  savePQVendor: SavePQVendor;
  dateNow: DateNowHandler;
  findApprovalGroup: FindApprovalGroup;
  saveApprovalGroups: SaveApprovalGroups;
  findCalendar: FindCalendar;
  saveDocumentHistory: SaveDocumentHistory;
}

export class Request {
  userLogin: UserLogin;
  pqId: string;
  index: "1" | "2" | "3";

  response: "AD" | "AI" | "PF" | "OK";
  dueDate: Date;

  failedProcess?: string;
  failedNotes?: string;

  comment: string;

  meetingDates: {
    vendorId: string;
    date: Date;
  }[];
}

export class Response { }

export const pqEvaluationSubmit: Usecase<Gateways, Request, Response> = {
  gateways: Gateways,
  request: Request,
  setup: (o) => async (ctx, req) => {
    const now = await o.dateNow(ctx);
    const pqTemplate = await o.findOnePQTemplate(ctx, { pqId: req.pqId });

    if (!pqTemplate) {
      throw new Error("pq not found");
    }

    if (!pqTemplate.phasesEvaluation) {
      throw new Error("pq phase evaluation not found");
    }

    const [pqVendors] = await o.findPQVendor(ctx, { pqId: req.pqId });

    if (req.index === "1") {
      // kalau semua PASS otomatis responsenya OK
      if (pqVendors.every((x) => x.phaseSubmission?.submission1?.resultSummary === "PASS")) {
        pqTemplate.phasesEvaluation.submission1!.response = "OK";
      } else {
        // kalau gak PASS berarti pilih salah satu dari "AD" | "AI" | "PF"
        pqTemplate.phasesEvaluation.submission1!.response = req.response;

        // kalau pilih AD
        if (req.response === "AD") {
          pqTemplate.phasesEvaluation.submission1!.dueDate = req.dueDate;
        }

        // ini cuman di submission3
        // pqTemplate.phasesEvaluation.submission1.failedProcess = req.failedProcess;
        // pqTemplate.phasesEvaluation.submission1.failedNotes = req.failedNotes;

        // kalau pilih AI
        if (req.response === "AI") {
          // cari semua vendor yang fail
          const failPqVendors = pqVendors.filter((x) => x.phaseSubmission?.submission1?.resultSummary === "FAIL");

          // dari inputan meeting dates
          req.meetingDates.forEach(async (x) => {
            // cari pq vendor yang vendor id nya di kasi di input
            const pqVendor = failPqVendors.find((y) => y.civdVendorId);
            if (pqVendor) {
              // masukkan meetingAdditionaInformationDate nya
              pqVendor.phaseSubmission!.submission1!.meetingAdditionalInformationDate = x.date;

              // update pq vendor
              await o.savePQVendor(ctx, pqVendor);
            }
          });
        }
      }

      await continueApproval(
        ctx,
        pqTemplate.phasesEvaluation.submission1!,
        now,
        req.userLogin,
        o.findApprovalGroup,
        o.saveApprovalGroups,
        o.findCalendar
      );
    } //
    else if (req.index === "2") {
      // kalau semua PASS otomatis responsenya OK
      if (pqVendors.every((x) => x.phaseSubmission?.submission2?.resultSummary === "PASS")) {
        pqTemplate.phasesEvaluation.submission2!.response = "OK";
      } else {
        // kalau gak PASS berarti pilih salah satu dari "AD" | "AI" | "PF"
        pqTemplate.phasesEvaluation.submission2!.response = req.response;

        // kalau pilih AD
        if (req.response === "AD") {
          pqTemplate.phasesEvaluation.submission2!.dueDate = req.dueDate;
        }

        // ini cuman di submission3
        // pqTemplate.phasesEvaluation.submission2.failedProcess = req.failedProcess;
        // pqTemplate.phasesEvaluation.submission2.failedNotes = req.failedNotes;

        // kalau pilih AI
        if (req.response === "AI") {
          // cari semua vendor yang fail
          const failPqVendors = pqVendors.filter((x) => x.phaseSubmission?.submission2?.resultSummary === "FAIL");

          // dari inputan meeting dates
          req.meetingDates.forEach(async (x) => {
            // cari pq vendor yang vendor id nya di kasi di input
            const pqVendor = failPqVendors.find((y) => y.civdVendorId);
            if (pqVendor) {
              // masukkan meetingAdditionaInformationDate nya
              pqVendor.phaseSubmission!.submission2!.meetingAdditionalInformationDate = x.date;

              // update pq vendor
              await o.savePQVendor(ctx, pqVendor);
            }
          });
        }
      }

      await continueApproval(
        ctx,
        pqTemplate.phasesEvaluation.submission2!,
        now,
        req.userLogin,
        o.findApprovalGroup,
        o.saveApprovalGroups,
        o.findCalendar
      );
    } //
    else if (req.index === "3") {
      // kalau semua PASS otomatis responsenya OK
      if (pqVendors.every((x) => x.phaseSubmission?.submission3?.resultSummary === "PASS")) {
        pqTemplate.phasesEvaluation.submission3!.response = "OK";
      } else {
        // kalau gak PASS berarti pilih salah satu dari "AD" | "AI" | "PF"
        pqTemplate.phasesEvaluation.submission3!.response = req.response;

        // kalau pilih AD
        if (req.response === "AD") {
          pqTemplate.phasesEvaluation.submission3!.dueDate = req.dueDate;
        }

        // ini cuman di submission3
        pqTemplate.phasesEvaluation.submission3!.failedProcess = req.failedProcess;
        pqTemplate.phasesEvaluation.submission3!.failedNotes = req.failedNotes;

        // kalau pilih AI
        if (req.response === "AI") {
          // cari semua vendor yang fail
          const failPqVendors = pqVendors.filter((x) => x.phaseSubmission?.submission3?.resultSummary === "FAIL");

          // dari inputan meeting dates
          req.meetingDates.forEach(async (x) => {
            // cari pq vendor yang vendor id nya di kasi di input
            const pqVendor = failPqVendors.find((y) => y.civdVendorId);
            if (pqVendor) {
              // masukkan meetingAdditionaInformationDate nya
              pqVendor.phaseSubmission!.submission3!.meetingAdditionalInformationDate = x.date;

              // update pq vendor
              await o.savePQVendor(ctx, pqVendor);
            }
          });
        }

        if (req.response === "PF") {
          // process failed, restore or delete all pq vendor?
        }
      }

      await continueApproval(
        ctx,
        pqTemplate.phasesEvaluation.submission3!,
        now,
        req.userLogin,
        o.findApprovalGroup,
        o.saveApprovalGroups,
        o.findCalendar
      );
    }

    await o.savePQTemplate(ctx, pqTemplate);

    await o.saveDocumentHistory(ctx, {
      documentId: pqTemplate.id!,
      documentType: "PQ_EVALUATION_" + req.index as TypeOf<typeof DocumentTemplate>,
      comment: req.comment,
      date: getDateOnly(now),
      message: `PQ Evaluation ${req.index} Submitted`,
      user: req.userLogin,
      id: `${pqTemplate.id.slice(4)}-${formatDateWithSecond(now)}`,
    });

    return {};
  },
};

const continueApproval = async (
  ctx: Context, 
  pqEvaluationSubmission: EvaluationSubmission, 
  now: Date, 
  userLogin: UserLogin,
  findApprovalGroup: FindApprovalGroup,
  saveApprovalGroups: SaveApprovalGroups,
  findCalendar: FindCalendar,
) => {
  const approval = getApproval(pqEvaluationSubmission.approval?.approvalGroup!, userLogin);
  if (approval) {
    approval.date = getDateOnly(now);
    approval.signer = userLogin;
    approval.status = "DONE";
  }

  const paralelAND = pqEvaluationSubmission.approval?.approvalGroup ? pqEvaluationSubmission.approval?.approvalGroup.approvals.every((x) => x.status === "DONE") : false;

  const autoApprove = await moveApproval(ctx, paralelAND, pqEvaluationSubmission.approval!, findApprovalGroup, saveApprovalGroups, findCalendar);

  return approval;
}
