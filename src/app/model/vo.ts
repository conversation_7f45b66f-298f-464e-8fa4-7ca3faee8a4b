import { release } from "os";
import { <PERSON><PERSON>and<PERSON> } from "../../framework/core.js";
import { ApprovalGroup } from "./model_approval.js";
import { Delegation } from "./model_delegation.js";
import { PrequalificationTemplate } from "./model_prequalification.js";
import { User } from "./model_user.js";

export type TypeOf<T extends readonly any[]> = T[number];

export const DocumentTemplate = [
  "PROC_PLAN_UPP",
  "PROC_PLAN_APP",
  "REQUISITION",
  "PQ_REQUIREMENT",
  "PQ_REGISTRATION",
  "PQ_CLARIFICATION",
  "PQ_EVALUATION_1",
  "PQ_EVALUATION_2",
  "PQ_EVALUATION_3",
  "PQ_EVALUATION_FINAL",
  "PQ_SUBMISSION",
] as const;

export const ValueRange = ["LESS_THAN_OR_EQUAL", "BETWEEN_VALUES", "GREATER_THAN"] as const;

export const Commodity = ["GOODS", "SERVICES"] as const;

export const CommodityService = ["OPS", "NON_OPS"] as const;

export const UserRole = ["STAFF", "HEAD", "MGR", "SMVP", "GM"] as const;

export const SubDocumentRequisition = ["REQUISITION", "OWNER_ESTIMATION", "DA_JUSTIFICATION", "HSE_RISK_ASSESSMENT", "INSURANCE_ASSESSMENT"] as const;

export const ApprovalStatus = ["NOT_STARTED", "PROCESSING", "DONE"] as const;

export const DocumentStatus = ["NOT_CREATED", "DRAFT", "ON_REVIEW", "APPROVED", "DROPPED", ""] as const;

export const ProcPlan = ["UPP", "APP"] as const;

export const Currency = ["IDR", "USD"] as const;

export const PlanCreated = ["NEW_PROC_PLAN", "CARRY_OVER"] as const;

export const Activity = ["ROUTINE", "PROJECT"] as const;

export const TenderMethod = ["SELF_MANAGE", "DIRECT_APPOINTMENT", "DIRECT_SELECTION", "JOIN_CONTRACT", "JOIN_TENDER"] as const; // JOIN_CONTRACT = SHARING_CONTRACT

export const WorkProgramReference = ["TechMOM", "WPnB", "AFE", "POD"] as const;

// export const Contract = ["LUMP_SUM", "UNIT_RATE", "COST_PLUS_FEE", "CALL_OFF_ORDER", "FIRM_COMMITMENT", "PRICE_AGREEMENT", "MULTI_STANDING_AGREEMENT"] as const;

export const Incoterms = ["NR", "CFR", "CIF", "CIP", "CPT", "DAP", "DAT", "DDP", "EXW", "FCA", "FOB"] as const;

export const Consequence = ["LOW", "MEDIUM", "HIGH"] as const;

export const HighestExperienceScore = ["1/3", "1/5"] as const;

export const PrequalificationType = ["ANNOUNCEMENT", "INVITATION"] as const;

export const ContractTypeEngagement = [
  "SHARING_CONTRACT",
  "STRATEGIC_ALLIANCE",
  "FIRM_COMMITMENT",
  "CALL_OFF_ORDER",
  "CONSIGNMENT",
  "PRICE_AGREEMENT",
  "MULTI_STANDING_AGREEMENT",
  "TECHNICAL_FRAMEWORK_CONTRACT",
] as const;

export const ContractTypePayment = ["LUMPSUM", "TURN_KEY", "PERCENTAGE", "COST_PLUS_FEE", "INCENTIVE", "UNIT_PRICE"] as const;

export const PreQualificationPhase = ["REQUIREMENT", "REGISTRATION", "EVALUATION", "CLARIFICATION"] as const;
export const PreQualificationState = ["PQ_REQUIREMENT_DRAFT", "PQ_REQUIREMENT_SUBMITTED"] as const;

export const BusinessClass = ["SMALL", "MEDIUM", "LARGE"] as const;

export const Business = [
  "MandatoryGoods",
  "NonMandatoryGoods",
  "NonAPDNGoods",
  "ConstructionServices",
  "OffshoreEPCIServices",
  "OffshoreDrillingServices",
  "VesselServices",
  "OtherServices",
] as const;

export const CompanyStatus = [
  "LocalCompany",
  "NationalCompany",
  "ConsortiumOfLocalCompanies",
  "ConsortiumOfLocalAndNationalCompanies",
  "ForeignCompany",
  "ConsortiumLocalAndNationalAndOrForeignCompanies",
] as const;

export interface VendorFiles {
  id: string;
  file: string;
}
[];

export interface Files {
  files: { id: string; file: string }[];
  url: string;
}

export interface SharepointFile {
  path: string;
  name: string;
}

export interface WorkProgramAndBudget {
  year: string;
  scheduleLines: {
    schedule: TypeOf<typeof WorkProgramAndBudgetSchedule>;
    lines: TypeOf<typeof TwoDigitNumerical>[];
  }[];
}

export interface AuthorizationForExpenditure {
  no: string;
  scheduleLines: {
    schedule: TypeOf<typeof AuthorizationForExpenditureSchedule>;
    lines: TypeOf<typeof TwoDigitNumerical>[];
  }[];
}

export interface ValueCurrency {
  value: number;
  currency: TypeOf<typeof Currency>;
}

export const deskripsiDokumenDomisili = [
  "NIP Berbasis Risiko",
  "Data Profil 6 Bulan Terakhir Perseroan Terbatas (PT) dari AHU Online",
  "Surat Keterangan Domisili/Izin Gangguan yang masih berlaku",
] as const;

export const deskripsiBuktiStatusPDN = [
  "SKUP Migas yang masih berlaku",
  "Pernyataan Status Perusahaan Dalam Negeri, dilengkapi dengan data profil 6 bulan terakhir dari AHU Online",
] as const;

export const dokumenK3LL = [
  "Salinan tanda terima dokumen VHSE-MS HCML",
  "Salinan Sertifikat K3LL yang masih berlaku sesuai dengan kualifikasi yang disyaratkan",
  "Formulit Kuesionar VHSE-MS yang telah diisi beserta lampiran dokumen-dokumen pendukungnya",
] as const;

export const WorkProgramAndBudgetSchedule = ["04", "08", "11"] as const;

export const AuthorizationForExpenditureSchedule = [
  "18A",
  "18B",
  "18C",
  "18D",
  "18E",
  "18F",
  "18G",
  "18H",
  "18I",
  "19",
  "20",
  "21A",
  "21B",
  "21C",
  "22A",
  "22B",
  "22C",
  "23",
  "24",
] as const;

export const TwoDigitNumerical = [
  "01",
  "02",
  "03",
  "04",
  "05",
  "06",
  "07",
  "08",
  "09",
  "10",
  "11",
  "12",
  "13",
  "14",
  "15",
  "16",
  "17",
  "18",
  "19",
  "20",
  "21",
  "22",
  "23",
  "24",
  "25",
  "26",
  "27",
  "28",
  "29",
  "30",
  "31",
  "32",
  "33",
  "34",
  "35",
  "36",
  "37",
  "38",
  "39",
  "40",
  "41",
  "42",
  "43",
  "44",
  "45",
  "46",
  "47",
  "48",
  "49",
  "50",
  "51",
  "52",
  "53",
  "54",
  "55",
  "56",
  "57",
  "58",
  "59",
  "60",
  "61",
  "62",
  "63",
  "64",
  "65",
  "66",
  "67",
  "68",
  "69",
  "70",
  "71",
  "72",
  "73",
  "74",
  "75",
  "76",
  "77",
  "78",
  "79",
  "80",
  "81",
  "82",
  "83",
  "84",
  "85",
  "86",
  "87",
  "88",
  "89",
  "90",
  "91",
  "92",
  "93",
  "94",
  "95",
  "96",
  "97",
  "98",
  "99",
] as const;

export const PotentialDamageParty = ["HCML", "CONTRACTOR", "THIRD_PARTY"] as const;

export const PotentialDamageType = ["DEATH", "INJURY", "DISEASE", "PHYSICAL", "POLLUTION"] as const;

export const PotentialDamagePollutionType = ["EXPLOTION", "SEEPAGE", "BLOW_OUT", "ENVIRONTMENT"] as const;

export const validateActionByID = (userLoginID: string, userIds: string[], userSectionId?: string | null, sectionId?: string | null) => {
  //
  if (userIds.some((id) => id === userLoginID)) {
    return;
  }

  if (sectionId && userSectionId === sectionId) {
    return;
  }
  throw new Error(`you are not authorized to do this action..`);
};

export const getEmptyValueCurrency = (): ValueCurrency[] => {
  return [
    { currency: "USD", value: 0 },
    { currency: "IDR", value: 0 },
  ];
};

export interface EligibilityAction {
  canCreate: boolean;
  canSubmit: boolean;
  canApproveOrSendBack: boolean;
  canSendBackToUPP?: boolean;
  canAdminUpdate?: boolean;
  canAdminRevertApproval?: boolean;
}

export interface EligibilityActionWithCanAssign extends EligibilityAction {
  canAssign: boolean;
}

export class EligibilityAction {
  constructor() {
    this.canApproveOrSendBack = false;
    this.canSubmit = false;
    this.canCreate = false;
    this.canSendBackToUPP = false;
    this.canAdminUpdate = false;
    this.canAdminRevertApproval = false;
  }
}

export class EligibilityActionWithCanAssign {
  constructor() {
    this.canApproveOrSendBack = false;
    this.canSubmit = false;
    this.canCreate = false;
    this.canAssign = false;
    this.canAdminUpdate = false;
    this.canAdminRevertApproval = false;
  }
}

export const validateEligibilityUPPAction = (
  status: TypeOf<typeof DocumentStatus>,
  userLogin: User,
  userIDs: string[],
  ag: ApprovalGroup | null,
  departmentId?: string | null,
  sectionId?: string | null,
  delegation?: Delegation | null
): EligibilityAction => {
  //
  const eligibility: EligibilityAction = new EligibilityAction();

  //
  if (departmentId && departmentId === userLogin.department?.id && (!status || status === "DRAFT")) {
    eligibility.canCreate = true;
  }

  if (status === "DRAFT" && userLogin.department?.code !== "") {
    try {
      validateActionByID(userLogin.id, userIDs);
      eligibility.canSubmit = true;
    } catch {
      //
    }
  }

  if (status === "DRAFT" && userLogin.section?.id === sectionId) {
    try {
      validateActionByID(userLogin.id, userIDs, userLogin.section!.id, sectionId);
      eligibility.canSubmit = true;
    } catch {
      //
    }
  }

  // TODO potential issue. after refactor require to check since we no longer depend on role anymore
  if (ag && status === "ON_REVIEW") {
    if (delegation && delegation.endDate! <= new Date()) {
      eligibility.canApproveOrSendBack = ag.approvals.some((app) => app.users?.some((u) => u.id === delegation.delegateToUserId) || delegation.id === app.position?.id);
    } else {
      eligibility.canApproveOrSendBack = ag.approvals.some((app) => app.users?.some((u) => u.id === userLogin.id) || userLogin.position?.id === app.position?.id);
    }
  }

  return eligibility;
};

export const HCMLAddress =
  "HCML Office, Indonesia Stock Exchange (IDX) Tower 1 Lt.24 dan 25, Jl. Jend. Sudirman No.Kav. 52, RT.5/RW.3, Senayan, Kec. Kby. Baru, Kota Jakarta Selatan, Daerah Khusus Ibukota Jakarta 12190";

export type DateNowHandler = ActionHandler<void, Date>;

export type RandomStringHandler = ActionHandler<void, string>;

export type ContextDataHandler = ActionHandler<string, any>;

// dateNow: async (ctx: Context, _: void) => {
//   return new Date();
// },
// randomString: async (ctx: Context, charCount?: number) => {
//   return generateID(charCount ?? undefined);
// },
// contextData: async (ctx: Context, fieldName: string) => {
//   return ctx.data[fieldName];
// },

export const getPhaseStatus = (currentPhase: TypeOf<typeof PreQualificationPhase> | undefined, item: PrequalificationTemplate, index?: "1" | "2" | "3") => {
  //

  // return "DRAFT";

  if (currentPhase === "REQUIREMENT") {
    return item.phasesRequirement?.status;
  }
  if (currentPhase === "REGISTRATION") {
    return item.phasesRegistration?.status;
  }

  if (currentPhase === "EVALUATION") {
    if (index === "1") {
      return item.phasesEvaluation?.submission1?.approval?.status;
    }

    if (index === "2") {
      return item.phasesEvaluation?.submission2?.approval?.status;
    }

    if (index === "3") {
      return item.phasesEvaluation?.submission3?.approval?.status;
    }
  }

  if (currentPhase === "CLARIFICATION") {
    return item.phasesClarification?.approval?.status;
  }
};

export const getApprover = (currentPhase: TypeOf<typeof PreQualificationPhase> | undefined, item: PrequalificationTemplate, index?: "1" | "2" | "3") => {
  if (currentPhase === "REQUIREMENT") {
    return item.phasesRequirement?.approvalGroup?.approvals[0].currentUserInPosition;
  }
  if (currentPhase === "REGISTRATION") {
    return item.phasesRegistration?.approvalGroup?.approvals[0].currentUserInPosition;
  }

  if (currentPhase === "EVALUATION") {
    if (index === "1") {
      return item.phasesEvaluation?.submission1?.approval?.approvalGroup?.approvals[0].currentUserInPosition;
    }

    if (index === "2") {
      return item.phasesEvaluation?.submission2?.approval?.approvalGroup?.approvals[0].currentUserInPosition;
    }

    if (index === "3") {
      return item.phasesEvaluation?.submission3?.approval?.approvalGroup?.approvals[0].currentUserInPosition;
    }
  }

  if (currentPhase === "CLARIFICATION") {
    return item.phasesClarification?.approval?.approvalGroup?.approvals[0].currentUserInPosition;
  }
};

export const ucwords = (str: string) => {
  //
  if (!str) {
    return str;
  }

  return str
    .toLowerCase() // Convert the entire string to lowercase
    .replace(/\b[a-z]/g, function (char) {
      return char.toUpperCase(); // Capitalize the first letter of each word
    });
};

export const formatNumber = (num: number | string) => {
  return num.toLocaleString("en-US", { minimumFractionDigits: 0, maximumFractionDigits: 0 });
};

export const departmentGetRBTBsExceptions = [
  "********", // 23 Strategic Planning & Performance
];

// Convert the filter object to URLSearchParams
export const toURLSearchParams = (filter: any): URLSearchParams => {
  const params = new URLSearchParams();

  Object.entries(filter).forEach(([key, value]) => {
    if (value !== undefined) {
      params.append(key, String(value));
    }
  });

  return params;
};

export type ExcelAPPDownload = {
  procPlanDetailId?: string;
  departmentName: string;
  sectionName: string;
  status: string;
  commodity: TypeOf<typeof Commodity> | string;
  activityType: TypeOf<typeof Activity> | string;
  procPlanCode: string;
  title: string;
  generalScopeOfWork: string;
  tenderMethod: TypeOf<typeof TenderMethod> | string;
  requisitionSubmissionDate: Date | string | null;
  tenderStartDate: Date | string | null;
  contractDateStart: Date | string | null;
  contractDateEnd: Date | string | null;
  poDateIssuance?: Date | string | null;
  poDateDelivery?: Date | string | null;
  durationType: string;
  durationValue: string;
  localContentLevel: number | string;
  currency: TypeOf<typeof Currency>;
  valueEstimation: number | string;
  estCurrYearExpenditure: number | string;
  approvalAnnualBudget: number | string;
  workProgramReferences: TypeOf<typeof WorkProgramReference>[];
  technicalMoMSubjectAndDate: string[] | string;
  remarks: string;
  schedule: string[] | string;
  line: string[] | string;
}

export type ExcelRequisitionDownload = {
  requisitionId?: string;
  departmentName: string;
  commodity: TypeOf<typeof Commodity> | string;
  procPlanDetailCode: string;
  tenderCode: string;
  title: string;
  requisitionSubmission: string;
  requisitionRelease: string;
  currency: TypeOf<typeof Currency>;
  value: number | string;
  localContentLevel: number;
  hseAssessment: string;
  status?: string;
  pic?: string;
}

export const excelAPPTemplateHeader = {
  departmentName: "Department",
  sectionName: "Section",
  status: "Status",
  commodity: "Commodity Type",
  activityType: "Type of Activity",
  procPlanCode: "Proc Plan No",
  title: "Title",
  generalScopeOfWork: "General Scope of Work",
  tenderMethod: "Preferred Tender Method",
  requisitionSubmissionDate: "Requisition Submission Date",
  tenderStartDate: "Tender Start Date",
  contractDateStart: "Contract Start Date",
  contractDateEnd: "Contract End Date",
  durationType: "Duration Type",
  durationValue: "Duration",
  localContentLevel: "% TKDN Min. Percentage",
  currency: "Currency",
  valueEstimation: "Proc Plan Value",
  estCurrYearExpenditure: "Est. Expenditure",
  approvalAnnualBudget: "Approved Budget",
  workProgramReferences: "Work Program Reference",
  technicalMoMSubjectAndDate: "Technical MoM Subject & Date (If Proc. Plan Value > USD 500 K or IDR 5 M)",
  remarks: "Remarks",
  schedule: "Schedule",
  line: "Lines",
}

export const excelRequisitionTemplateHeader = {
  departmentName: "Department",
  commodity: "Goods / Services",
  procPlanDetailCode: "Proc Plan No",
  tenderCode: "Tender No",
  title: "Title",
  requisitionSubmission: "Requisition Submission Date",
  requisitionRelease: "Requisition Release Date",
  currency: "Currency",
  value: "OE Value",
  localContentLevel: "TKDN Min Requirement (%)",
  hseAssessment: "HSSE Risk Assesment",
  status: "Status",
  pic: "PIC",
}

export const budgetOwnerDepartments1 = [
  // <EMAIL> - 80141103 (Primary)
  "70060000", // Health, Safety, Security & Environment (HSSE)
  "70260000", // Maintenance
  "70050000", // Project MDA-MBH-MDK
  "70160000", // Project MAC-NFD
  "70140000", // Drilling & Completion (Drilling, Completion & Well Integrity)
  "********", // Logistic (Supply Chain Management)
  "********", // Internal Audit & Compliance
  "********", // Subsurface
  // "********", "********", "********", "********", "********", "********", "********" // Executive,
];

export const budgetOwnerDepartments2 = [
  // <EMAIL> - ******** (Primary)
  "********", // Engineering & Constructions (Engineering & Asset Integrity)
  "********", // Production MDA-MBH-MDK-MAC
  "********", // Production BD
  "********", // Finance & Accounting (Finance, Accounting & Tax)
  "********", // Legal
  "********", // Commercial & Planning
  "********", // Business Process & Technology
  "********", // HR & GA,     
  "********", // Relation & ROR, // Regional Office & Relations
  "********", // Budget & Reporting, 
  "********", // Strategic Planning & Performance,
  "********", // Marketing
];