import { Usecase } from "../../framework/core.js";
import { FindOnePQTemplate, PQMeetingInvitedVendors, SavePQTemplate } from "../model/model_prequalification.js";
import { DateNowHandler, Files, SharepointFile } from "../model/vo.js";

class Gateways {
  findOnePQTemplate: FindOnePQTemplate;
  savePQTemplate: SavePQTemplate;
  dateNow: DateNowHandler;
}

export class Request {
  pqId: string;
  pqMeetingInfoPayload: {
    place: string;
    deadlineForSubmission: Date;
    minutesOfMeeting: string[];
    invitedVendorsPqMeeting: PQMeetingInvitedVendors[];
    files: SharepointFile;
  };
}

export class Response {}

export const pqMeetingSubmit: Usecase<Gateways, Request, Response> = {
  gateways: Gateways,
  request: Request,
  setup: (o) => async (ctx, req) => {
    const pq = await o.findOnePQTemplate(ctx, req);
    const now = await o.dateNow(ctx);

    if (!pq) {
      throw new Error(`PQ Template with id ${req.pqId} is not found`);
    }

    if (!pq.pqMeetingDate) {
      throw new Error(`PQ Template with id ${req.pqId} does not have meeting date`);
    }

    // if (pq.currentPhase !== "REGISTRATION" || now > pq.pqMeetingDate) {
    //   throw new Error(`PQ Template with id ${req.pqId} is not in REGISTRATION or MEETING phase or MEETING date has expired`);
    // }

    await o.savePQTemplate(ctx, {
      ...pq,
      pqMeetingInfo: {
        status: "SUBMITTED",
        place: req.pqMeetingInfoPayload.place,
        deadlineForSubmission: req.pqMeetingInfoPayload.deadlineForSubmission,
        minutesOfMeeting: req.pqMeetingInfoPayload.minutesOfMeeting,
        invitedVendorsPqMeeting: req.pqMeetingInfoPayload.invitedVendorsPqMeeting,
        files: req.pqMeetingInfoPayload.files,
      },
    });

    return {};
  },
};
