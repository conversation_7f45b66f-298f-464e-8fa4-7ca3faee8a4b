import { Usecase } from "../../framework/core.js";
import { DeleteApprovalGroups, FindApprovalGroup, SaveApprovalGroups, getApprovalList } from "../model/model_approval.js";
import { FindApprovalTemplateGroup } from "../model/model_approval_template.js";
import { FindOneVendorCIVD, vendorCIVDLogin } from "../model/model_civd_vendor.js";
import { FindDelegation } from "../model/model_delegation.js";
import { FindOnePQTemplate, FindOnePQVendor, SavePQVendor } from "../model/model_prequalification.js";
import { FindUser } from "../model/model_user.js";
import { Date<PERSON>ow<PERSON>and<PERSON>, RandomStringHandler, SharepointFile, TypeOf, deskripsiDokumenDomisili } from "../model/vo.js";
import { getDateOnly } from "../utility/helper.js";

class Gateways {
  findOnePQVendor: FindOnePQVendor;
  savePQVendor: SavePQVendor;
  findOnePQTemplate: FindOnePQTemplate;
  findOneVendorCIVD: FindOneVendorCIVD;
  findApprovalTemplateGroup: FindApprovalTemplateGroup;
  findApprovalGroup: FindApprovalGroup;
  saveApprovalGroups: SaveApprovalGroups;
  deleteApprovalGroups: DeleteApprovalGroups;
  findUser: FindUser;
  findDelegation: FindDelegation;
  randomString: RandomStringHandler;
  dateNow: DateNowHandler;
}

export class Request {
  pqId: string;
  vendorCIVDLogin: vendorCIVDLogin;
  suratMinat?: SharepointFile; // registration a
  suratKuasaMinat?: SharepointFile;
  suratSPDA?: SharepointFile; // registration b
  suratIzinUsaha?: SharepointFile; // registration d
  sertifikatTKDN?: SharepointFile;
  dokumenDomisili?: SharepointFile; // registration c
  dokumenDomisiliDeskripsi?: TypeOf<typeof deskripsiDokumenDomisili>;
}

export class Response {}

export const vendorPQRegistrationUpdate: Usecase<Gateways, Request, Response> = {
  gateways: Gateways,
  request: Request,
  setup: (o) => async (ctx, req) => {
    const now = await o.dateNow(ctx);

    const vendor = await o.findOneVendorCIVD(ctx, req.vendorCIVDLogin.civdVendorId);
    if (!vendor) {
      throw new Error("vendor not found");
    }

    const pqTemplate = await o.findOnePQTemplate(ctx, { pqId: req.pqId });
    if (!pqTemplate) {
      throw new Error("pq not found");
    }

    if (!pqTemplate.pqRegistrationDate) {
      throw new Error("pq registration date not found");
    }

    // validate pq registration due date
    if (getDateOnly(now) > pqTemplate.pqRegistrationDate) {
      // throw new Error("pq registration date is expired");
    }

    const pqVendor = await o.findOnePQVendor(ctx, { pqId: req.pqId, civdVendorId: req.vendorCIVDLogin.civdVendorId });
    if (!pqVendor) {
      throw new Error("pq vendor not found");
    }

    if (pqVendor.status !== "REGISTRATION_SUBMIT") {
      throw new Error("pq vendor is ON REVIEW state");
    }

    // TODO::don't remove sold value remarks
    // registration clarification / evaluation
    pqVendor.phaseRegistration!.suratMinat = req.suratMinat
      ? { ...pqVendor.phaseRegistration!.suratMinat, files: req.suratMinat, result: "UNDECIDED" }
      : pqVendor.phaseRegistration!.suratMinat;
    pqVendor.phaseRegistration!.suratKuasaMinat = req.suratKuasaMinat
      ? { ...pqVendor.phaseRegistration?.suratKuasaMinat, files: req.suratKuasaMinat, result: "UNDECIDED" }
      : pqVendor.phaseRegistration?.suratKuasaMinat;
    pqVendor.phaseRegistration!.suratSPDA = req.suratSPDA
      ? { ...pqVendor.phaseRegistration!.suratSPDA, files: req.suratSPDA, result: "UNDECIDED" }
      : pqVendor.phaseRegistration!.suratSPDA;
    pqVendor.phaseRegistration!.dokumenDomisili = req.dokumenDomisili
      ? { ...pqVendor.phaseRegistration!.dokumenDomisili, files: req.dokumenDomisili, result: "UNDECIDED", description: req.dokumenDomisiliDeskripsi }
      : pqVendor.phaseRegistration!.dokumenDomisili;
    pqVendor.phaseRegistration!.suratIzinUsaha = req.suratIzinUsaha
      ? { ...pqVendor.phaseRegistration!.suratIzinUsaha, files: req.suratIzinUsaha, result: "UNDECIDED" }
      : pqVendor.phaseRegistration!.suratIzinUsaha;
    pqVendor.phaseRegistration!.sertifikatTKDN = req.sertifikatTKDN
      ? { ...pqVendor.phaseRegistration!.sertifikatTKDN, files: req.sertifikatTKDN, result: "UNDECIDED" }
      : pqVendor.phaseRegistration!.sertifikatTKDN;

    pqVendor.phaseRegistration!.evaluationDate = null;

    await o.savePQVendor(ctx, {
      ...pqVendor,
      status: "REGISTRATION_DRAFT",
      phaseRegistration: {
        ...pqVendor.phaseRegistration!,
        resultSummary: "UNDECIDED",
        submitDate: getDateOnly(now),
      },
    });

    // reset approval group and create new
    const approvals = await getApprovalList(
      ctx,
      `Approval for PQ Vendor ${pqVendor.id}`,
      o.findUser,
      o.findApprovalTemplateGroup,
      o.findDelegation,
      [],
      pqVendor.id,
      "PQ_REGISTRATION",
      new Date().getFullYear()
    );
    approvals[0].status = "NOT_STARTED";
    approvals[0].approvals[0].status = "NOT_STARTED";

    await o.deleteApprovalGroups(ctx, { documentId: pqVendor.id, documentType: "PQ_REGISTRATION" });
    await o.saveApprovalGroups(ctx, approvals);

    return {};
  },
};
