import { Usecase } from "../../framework/core.js";
import { FindOnePQVendor, PrequalificationVendor, VendorPhasePQClarification } from "../model/model_prequalification.js";

class Gateways {
  findOnePQVendor: FindOnePQVendor;
}

export class Request {
  pqId: string;
  vendorId: number;
}

export class Response {
  prequalificationVendor: VendorPhasePQClarification | null;
}

export const vendorPQClarificationGetOne: Usecase<Gateways, Request, Response> = {
  gateways: Gateways,
  request: Request,
  setup: (o) => async (ctx, req) => {
    const pqVendor = await o.findOnePQVendor(ctx, {...req, civdVendorId: req.vendorId });

    if (
      !pqVendor ||
      pqVendor.prequalificationTemplate.currentPhase !== "CLARIFICATION" ||
      !pqVendor.phaseClarification ||
      !pqVendor.phaseClarification.clarificationLetter
    ) {
      return { prequalificationVendor: null };
    }

    var phaseSubmission = null;
    if (pqVendor.phaseSubmission!.submission3 && pqVendor.phaseSubmission!.submission3!.resultSummary === "FAIL") {
      phaseSubmission = pqVendor.phaseSubmission!.submission3;
    } else if (pqVendor.phaseSubmission!.submission2 && pqVendor.phaseSubmission!.submission2!.resultSummary === "FAIL") {
      phaseSubmission = pqVendor.phaseSubmission!.submission2;
    } else if (pqVendor.phaseSubmission!.submission1 && pqVendor.phaseSubmission!.submission1!.resultSummary === "FAIL") {
      phaseSubmission = pqVendor.phaseSubmission!.submission1;
    } else {
      return { prequalificationVendor: null };
    }

    const pqClarification: VendorPhasePQClarification = {
      clarificationLetter: {
        files: pqVendor.phaseClarification.clarificationLetter.files && null,
        date: pqVendor.phaseClarification.clarificationLetter.date && null,
        number: pqVendor.phaseClarification.clarificationLetter.number && null,
        subject: pqVendor.phaseClarification.clarificationLetter.subject && null,
      },
      clarificationDocuments: {
        entity: pqVendor.phaseClarification.clarificationDocuments!.entity && phaseSubmission.entity,
        companyEntities: pqVendor.phaseClarification.clarificationDocuments!.companyEntities && phaseSubmission.companyEntities,
        suratPernyataanPQ: pqVendor.phaseClarification.clarificationDocuments!.suratPernyataanPQ && phaseSubmission.suratPernyataanPQ,
        suratKuasaPernyataanPQ: pqVendor.phaseClarification.clarificationDocuments!.suratKuasaPernyataanPQ && phaseSubmission.suratKuasaPernyataanPQ,
        suratSPDA: pqVendor.phaseClarification.clarificationDocuments!.suratSPDA && phaseSubmission.suratSPDA,
        dokumenBuktiStatusPDN: pqVendor.phaseClarification.clarificationDocuments!.dokumenBuktiStatusPDN && phaseSubmission.dokumenBuktiStatusPDN,
        dokumenDomisili: pqVendor.phaseClarification.clarificationDocuments!.dokumenDomisili && phaseSubmission.dokumenDomisili,
        suratIzinUsaha: pqVendor.phaseClarification.clarificationDocuments!.suratIzinUsaha && phaseSubmission.suratIzinUsaha,
        sertifikatTKDN: pqVendor.phaseClarification.clarificationDocuments!.sertifikatTKDN && phaseSubmission.sertifikatTKDN,
        suratPerjanjianKonsorsium: pqVendor.phaseClarification.clarificationDocuments!.suratPerjanjianKonsorsium && phaseSubmission.suratPerjanjianKonsorsium,
        dokumenK3LL: pqVendor.phaseClarification.clarificationDocuments!.dokumenK3LL && phaseSubmission.dokumenK3LL,
        summaryExperiences: pqVendor.phaseClarification.clarificationDocuments!.summaryExperiences && phaseSubmission.summaryExperiences,
        dokumenEvaluasiKemampuanFinansial:
          pqVendor.phaseClarification.clarificationDocuments!.dokumenEvaluasiKemampuanFinansial && phaseSubmission.dokumenEvaluasiKemampuanFinansial,
        dokumenLaporanKeuangan: pqVendor.phaseClarification.clarificationDocuments!.dokumenLaporanKeuangan && phaseSubmission.dokumenLaporanKeuangan,
        otherDocuments: pqVendor.phaseClarification.clarificationDocuments!.otherDocuments && phaseSubmission.otherDocuments,
        meetingAdditionalInformationDate:
          pqVendor.phaseClarification.clarificationDocuments!.meetingAdditionalInformationDate && phaseSubmission.meetingAdditionalInformationDate,
        submitDate: pqVendor.phaseClarification.clarificationDocuments!.submitDate && phaseSubmission.submitDate,
        resultSummary: pqVendor.phaseClarification.clarificationDocuments!.resultSummary && phaseSubmission.resultSummary,
        isPublish: pqVendor.phaseClarification.clarificationDocuments!.isPublish && phaseSubmission.isPublish,
        evaluationDate: pqVendor.phaseClarification.clarificationDocuments!.evaluationDate && null,
      },
    };

    return { prequalificationVendor: pqClarification };
  },
};
