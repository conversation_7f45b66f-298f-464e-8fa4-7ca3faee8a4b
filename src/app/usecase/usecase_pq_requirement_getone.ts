import { Usecase } from "../../framework/core.js";
import { C<PERSON><PERSON>Vendor, FindVendorCIVD, transformCIVDVendors } from "../model/model_civd_vendor.js";
import { FindOnePQTemplate, PrequalificationTemplate } from "../model/model_prequalification.js";
import { UserLogin } from "../model/model_user.js";
import { getApprover, getPhaseStatus } from "../model/vo.js";

class Gateways {
  findOnePQTemplate: FindOnePQTemplate;
  findVendorCIVD: FindVendorCIVD;
}

export class Request {
  userLogin: UserLogin;
  pqId: string;
  index?: "1" | "2" | "3";
}

export class Response {
  prequalificationTemplate: PrequalificationTemplate & { invitedCIVDVendors: CIVDVendor[] } | null;
}

export const pqRequirementGetOne: Usecase<Gateways, Request, Response> = {
  gateways: Gateways,
  request: Request,
  setup: (o) => async (ctx, req) => {
    const pq = await o.findOnePQTemplate(ctx, req);

    if (pq === null) {
      return {
        prequalificationTemplate: null,
        eligibility: {
          canSubmit: false,
          canApproveAndSendback: false,
        },
      };
    }

    const phaseStatus = getPhaseStatus(pq?.currentPhase, pq, req.index);
    const canSubmit = phaseStatus === "DRAFT" && [...pq.requestedBackToBacks.map((x) => x.id), pq.assignedUser?.id].some((id) => id === req.userLogin.id);

    const approver = getApprover(pq?.currentPhase, pq, req.index);
    const canApproveAndSendback = phaseStatus === "ON_REVIEW" && approver?.id === req.userLogin.id;

    let civdVendors: CIVDVendor[] = [];

    if (pq.invitedVendors && pq.invitedVendors.length > 0) {
      const [vendors] = await o.findVendorCIVD(ctx, { civdIds: pq.invitedVendors });
      civdVendors = transformCIVDVendors(vendors);
    }

    return {
      prequalificationTemplate: {
        ...pq,
        invitedCIVDVendors: civdVendors,
      },
      eligibility: {
        canSubmit,
        canApproveAndSendback,
      },
    };
  },
};
