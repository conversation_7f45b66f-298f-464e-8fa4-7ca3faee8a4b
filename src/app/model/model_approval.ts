import { Context } from "../../framework/core.js";
import { BaseFindManyFilter, DeleteEntity, FindManyEntity, SaveEntity } from "../../framework/repository.js";
import { implFindAllDelegation } from "../gatewayimpl/impl_delegation.js";
import { ApprovalTemplateGroup, ApprovalTemplateRule, FindApprovalTemplateGroup } from "./model_approval_template.js";
import { FindCalendar } from "./model_calendar.js";
import { Delegation, DelegationUser, FindDelegation } from "./model_delegation.js";
import { Position } from "./model_position.js";
import { FindUser, FindUserSupervisors, User } from "./model_user.js";
import { ApprovalStatus, budgetOwnerDepartments1, budgetOwnerDepartments2, DocumentStatus, DocumentTemplate, EligibilityAction, SubDocumentRequisition, TypeOf } from "./vo.js";

export interface Approval {
  id?: string;
  status?: TypeOf<typeof ApprovalStatus>;
  date?: Date | null;
  position?: Position | null; // hanya untuk struktural
  currentUserInPosition?: UserWithDelegation | null;
  users?: UserWithDelegation[]; // hanya untuk non structural
  as?: string | null; // hanya untuk non structural
  signer?: User | null;
  subDocumentType?: TypeOf<typeof SubDocumentRequisition>;
}

export interface ApprovalGroup {
  id?: string;
  documentId?: string;
  documentType?: TypeOf<typeof DocumentTemplate>;
  sequence?: number;
  approvals: Approval[];
  status?: TypeOf<typeof ApprovalStatus>;
  description?: string;
  nextApprovalGroupId?: string | null;
  durationDays?: number;
  documentYear?: number;
}

type UserWithDelegation = User & {
  delegation?: DelegationUser | null
};

export class HasApprovalGroup {
  status: TypeOf<typeof DocumentStatus>;
  isSendBack: boolean;
  approvalGroup: ApprovalGroup | null;
}

export class FindApprovalGroupFilter extends BaseFindManyFilter {
  documentId?: string;
  documentType?: TypeOf<typeof DocumentTemplate>;
  userId?: string;
  id?: string;
  positionId?: string;
  status?: TypeOf<typeof ApprovalStatus>;
  statuses?: TypeOf<typeof ApprovalStatus>[];
  role?: string;
  delegationUserApproval?: boolean;
}

type UserWithDurationDays = {
  user: User;
  durationDays?: number | null;
}

export type SaveApprovalGroups = SaveEntity<ApprovalGroup[]>;
export type DeleteApprovalGroups = DeleteEntity<{ documentId: string; documentType?: TypeOf<typeof DocumentTemplate> }>;
export type FindApprovalGroup = FindManyEntity<ApprovalGroup, FindApprovalGroupFilter>;
export type FindApprovalGroupNative = FindManyEntity<ApprovalGroup, FindApprovalGroupFilter>;

export type ProcPlanDetailMail = {
  department: string;
  section: string;
  quantity: number;
  value: number | string; // USD
  url: string;
};

export const getApprovalWithRequestForRuleList = async (
  ctx: Context,
  description: string,
  assignedUserPositionId: string,
  requestForUserPositionId: string,
  findUserSupervisors: FindUserSupervisors,
  findApprovalTemplateGroup: FindApprovalTemplateGroup,
  docId: string,
  docType: TypeOf<typeof DocumentTemplate>,
  docYear: number
): Promise<ApprovalGroup[]> => {
  const [assignedApprovals] = await findUserSupervisors(ctx, { positionId: assignedUserPositionId });
  const [requestForApprovals] = await findUserSupervisors(ctx, { positionId: requestForUserPositionId });
  const [atgs] = await findApprovalTemplateGroup(ctx, { documentType: docType });

  const approvalGroupList: Approval[] = atgs.map((atg, i) => {
    const users = (atg.approvalTemplates[0].rule?.isRequestFor ? requestForApprovals : assignedApprovals).find(
      (val) => val.position?.role === atg.approvalTemplates[0].role
    );

    return {
      as: users?.position?.name,
      position: users?.position,
      currentUserInPosition: { ...users! },
      date: null,
      status: i === 0 ? "PROCESSING" : "NOT_STARTED",
      signer: null,
    };
  });

  const resultApprovalGroups: ApprovalGroup[] = [];
  let sequenceCount = 1;

  approvalGroupList.forEach((agl, i) => {
    if (agl.as && agl.currentUserInPosition) {
      resultApprovalGroups.push({
        approvals: [agl],
        id: `${docId}-${sequenceCount.toString().padStart(2, "0")}`,
        nextApprovalGroupId:
          approvalGroupList.length === 1 || i === approvalGroupList.length - 1 ? null : `${docId}-${(sequenceCount + 1).toString().padStart(2, "0")}`,
        documentId: docId,
        documentType: docType,
        sequence: sequenceCount,
        status: i === 0 ? "PROCESSING" : "NOT_STARTED",
        documentYear: docYear,
        description,
      });
      sequenceCount++;
    }
  });

  return resultApprovalGroups;
};

export const getApprovalList = async (
  ctx: Context,
  description: string,
  findUser: FindUser,
  findApprovalTemplateGroup: FindApprovalTemplateGroup,
  findDelegation: FindDelegation,
  listUser: User[],
  docId: string,
  docType: TypeOf<typeof DocumentTemplate>,
  docYear: number,
  docRule?: ApprovalTemplateRule,
): Promise<ApprovalGroup[]> => {
  //

  const [atgs] = await findApprovalTemplateGroup(ctx, { documentType: docType });

  const approvalGroupList = constructApprovalList(atgs, listUser, docRule);

  let resultApprovalGroups: ApprovalGroup[] = [];

  const [users] = await findUser(ctx, { ids: approvalGroupList.flatMap((atg) => atg.approvals.flatMap((at) => at.users?.map((u) => u.id))) as string[] });

  approvalGroupList.forEach((atg, i) => {
    //
    const resultApprovals: Approval[] = [];

    atg.approvals.forEach((app) => {
      //
      app.users = app.users?.map((au) => users.find((u) => u.id === au.id)) as User[];

      resultApprovals.push({
        ...app, // position, as, users
        date: null,
        status: i === 0 ? "PROCESSING" : "NOT_STARTED",
        signer: null,
      });
    });

    resultApprovalGroups.push({
      approvals: resultApprovals,
      id: `${docId}-${(i + 1).toString().padStart(2, "0")}`,
      nextApprovalGroupId: approvalGroupList.length === 1 || i === approvalGroupList.length - 1 ? null : `${docId}-${(i + 2).toString().padStart(2, "0")}`,
      documentId: docId,
      documentType: docType,
      sequence: i + 1,
      status: i === 0 ? "PROCESSING" : "NOT_STARTED",
      documentYear: docYear,
      description,
    });
  });

  // assign delegation
  if (resultApprovalGroups.length > 0) {
    resultApprovalGroups = await assignDelegation(ctx, resultApprovalGroups, findUser, findDelegation);    
  }

  return resultApprovalGroups;
};

export const constructApprovalList = (atss: ApprovalTemplateGroup[], us: User[], docRule?: ApprovalTemplateRule): ApprovalGroup[] => {
  //

  const ag: ApprovalGroup[] = [];

  // struktural ditandakan dengan adanya positionId
  // non struktural ditandakan dengan tidak adanya positionId tapi adanya userId

  atss.forEach((ats) => {
    //
    const ap: Approval[] = [];
    // const uniqueUserSet = new Set<string>(); //

    ats.approvalTemplates.forEach((at) => {
      //
      // as non-struktural based on user_id's
      if (at.users) {
        const userList: User[] = [];

        at.users.forEach((a) => {
          // if (uniqueUserSet.has(a.id)) {
          //   return;
          // }
          userList.push(a);
          // uniqueUserSet.add(a.id);
        });

        if (!ruleSatisfied(at.rule, docRule, at.subDocumentType) || userList.length === 0) {
          return;
        }
        //
        ap.push({ users: userList, as: at.as, subDocumentType: at.subDocumentType });

        return;
      }
      //
      // struktural based on role
      if (at.role) {
        // rule di requisition
        if (at.rule?.isRequestFor) {
          const u = us[0];
          ap.push({ position: u.position, currentUserInPosition: u, as: u.position?.name, subDocumentType: at.subDocumentType });
          return;
        }

        for (const u of us) {
          // }
          // us.forEach((u) => {
          if (!ruleSatisfied(at.rule, docRule, at.subDocumentType) || at.role !== u.position?.role) {
            continue;
          }

          if (ap.length === 0 || (ap.length > 0 && ap[ap.length - 1].position!.id !== u.position?.id)) {
            ap.push({ position: u.position, currentUserInPosition: u, as: u.position?.name, subDocumentType: at.subDocumentType });
            break;
          }
          // });
        }
        return;
      }
    });

    if (ap.length > 0) {
      ag.push({ approvals: ap });
    }
  });

  return ag;
};

const ruleSatisfied = (atRule?: ApprovalTemplateRule, docRule?: ApprovalTemplateRule, subDocumentType?: TypeOf<typeof SubDocumentRequisition>): boolean => {
  //
  if (atRule && docRule) {
    //
    for (const key in atRule) {
      //

      if (key === "commodity" && docRule.commodity !== atRule.commodity) {
        return false;
      }

      if (key === "commodityServiceType" && docRule.commodityServiceType !== atRule.commodityServiceType) {
        return false;
      }

      if (key === "isAdditionalProcPlan" && docRule.isAdditionalProcPlan !== atRule.isAdditionalProcPlan) {
        return false;
      }

      if (key === "budgetOwners" && !docRule.budgetOwners?.some((x) => atRule.budgetOwners?.includes(x))) {
        return false;
      }

      if (key === "isRequestFor" && docRule.isRequestFor !== atRule.isRequestFor) {
        return false;
      }

      if (key === "isValueGreaterThanMinRequisitionValue") { // for LCL / Local Content
        if (
          docRule.isValueGreaterThanMinRequisitionValue !== atRule.isValueGreaterThanMinRequisitionValue &&
          !docRule.isCommodityGoodsAndFirmCommitment
        ) {
          return false;
        }
      }

      if (subDocumentType && subDocumentType === "DA_JUSTIFICATION") {
        if (key === "isDaJustification" && docRule.isDaJustification !== atRule.isDaJustification) {
          return false;
        }

        if (key === "isValueGreaterThanMaxDaValue" && docRule.isValueGreaterThanMaxDaValue !== atRule.isValueGreaterThanMaxDaValue) {
          return false;
        }
      }

      if (key === "isValueGreaterThanMinOeValue") { // SMVP Request for
        if (
          docRule.isValueGreaterThanMinOeValue !== atRule.isValueGreaterThanMinOeValue &&
          !docRule.isDaJustification
        ) {
          return false;
        }
      }

      if (key === "isValueGreaterThanMaxOeValue") { // GM
        if (
          docRule.isValueGreaterThanMaxOeValue !== atRule.isValueGreaterThanMaxOeValue &&
          !docRule.isDaJustification
        ) {
          return false;
        }
      }

      if (key === "isValueGreaterThanMaxDaValue") { // SCM / Supply Chain Management
        if (
          docRule.isValueGreaterThanMaxDaValue !== atRule.isValueGreaterThanMaxDaValue &&
          !docRule.isDaJustification
        ) {
          return false;
        }
      }
    }
  }

  return true;
};

export const validateApprovalAction = (userLogin: User, approvalGroup: ApprovalGroup | null, approvalTemplateGroup: ApprovalTemplateGroup | null = null, delegation: Delegation | null = null) => {
  if (approvalTemplateGroup) {
    if (approvalTemplateGroup.approvalTemplates.some((at) => at.users?.some((u) => u.id === userLogin.id) || at.role === userLogin.position?.role)) {
      return;
    }
  }

  if (!approvalGroup) {
    throw new Error("no approval anymore");
  }

  for (const app of approvalGroup.approvals) {
    // check delegation users
    let delegation = activeDelegation(app);
    if (delegation && delegation.user.id === userLogin.id) {
      return;
    }

    //nonstruktual user
    if (app.users?.some((x) => x.id === userLogin.id)) {
      return;
    }

    // struktural user
    if (app.position?.id === userLogin.position?.id) {
      return;
    }
  }

  throw new Error("you are not authorized to do the action");
};

export function getApproval(approvalGroup: ApprovalGroup, userLogin: User) {
  let paralelOR = true;
  for (const app of approvalGroup?.approvals!) {
    // type users
    if (app.users) {
      for (const user of app.users) {
        if (user.id === userLogin.id) {
          paralelOR = true;
          break;
        } 
        // check delegation
        else if (user.delegation?.user.id === userLogin.id) {
          paralelOR = true;
          break;
        }
      }
    }

    // type role
    if (!paralelOR && app.position?.id === userLogin.position?.id) {
      paralelOR = true;
    }

    // check delegation for type role
    if (!paralelOR && app.currentUserInPosition?.delegation?.user.id === userLogin.id) {
      paralelOR = true;
    }

    // return current user
    if (paralelOR) {
      // 
      if (approvalGroup.approvals.length > 1) {
        // 
        if (app.users) {
          return approvalGroup.approvals.find(
            (app) => app.users?.find(
              (user) => user.id === userLogin.id
            ) || app.users?.find(
              (user) => user.delegation?.user.id === userLogin.id
            )
          );
        }
  
        if (app.currentUserInPosition) {
          return approvalGroup.approvals.find(
            (app) => app.currentUserInPosition?.id === userLogin.id ||
              app.currentUserInPosition?.delegation?.user.id === userLogin.id
          );
        }
      }

      return app;
    }
  }

  return null;
}

export async function moveApproval(
  //
  ctx: Context,
  paralelAND: boolean,
  docHasApproval: HasApprovalGroup,
  findApproval: FindApprovalGroup,
  saveApprovals: SaveApprovalGroups,
  findCalendar: FindCalendar | null = null
) {
  //
  let autoApprove = false;

  if (docHasApproval.approvalGroup!.nextApprovalGroupId && paralelAND) {
    //
    docHasApproval.approvalGroup!.status = "DONE";
    docHasApproval.approvalGroup!.durationDays = await getLastApprovalGroupDurationDays(ctx, findApproval, docHasApproval.approvalGroup, new Date());

    const nextApproval = await findNextApprovalObject(ctx, findApproval, docHasApproval.approvalGroup!.nextApprovalGroupId!);

    nextApproval.status = "PROCESSING";
    nextApproval.approvals.forEach((approval) => {
      approval.status = "PROCESSING";
    });

    await saveApprovals(ctx, [docHasApproval.approvalGroup!, nextApproval]);

    docHasApproval.status = "ON_REVIEW";
    docHasApproval.isSendBack = false;
    docHasApproval.approvalGroup = nextApproval;
    // await saveProcPlanHeader(ctx, pph);
    //
  } else {
    docHasApproval.approvalGroup!.status = "DONE";

    // no next approval means that auto approve
    await saveApprovals(ctx, [docHasApproval.approvalGroup!]);

    // auto approve!
    docHasApproval.status = "APPROVED";
    docHasApproval.isSendBack = false;
    docHasApproval.approvalGroup = null;
    // await saveProcPlanHeader(ctx, pph);

    autoApprove = true;
  }

  return autoApprove;
}

export const findNextApprovalObject = async (ctx: Context, findApproval: FindApprovalGroup, nextApprovalGroupId: string) => {
  const [nextApprovals] = await findApproval(ctx, { id: nextApprovalGroupId });
  const nextApproval = nextApprovals.length > 0 ? nextApprovals[0] : null;
  if (!nextApproval) {
    throw new Error("next approval should be exist");
  }
  return nextApproval;
};

export const validateEligibilityAPPAction = (
  status: TypeOf<typeof DocumentStatus>,
  userLogin: User,
  ag: ApprovalGroup | null,
  atg: ApprovalTemplateGroup | null,
  delegation: Delegation | null = null
): EligibilityAction => {
  //
  const eligibility: EligibilityAction = new EligibilityAction();

  if (!status || status === "DRAFT") {
    eligibility.canCreate = atg
      ? atg.approvalTemplates.some((at) => at.users?.some((u) => u.id === userLogin.id) || at.role === userLogin.position?.role)
      : false;
  }

  if (status === "DRAFT") {
    try {
      validateApprovalAction(userLogin, ag, atg);
      eligibility.canSubmit = true;
    } catch {
      //
    }
  }

  if (ag && status === "ON_REVIEW") {
    if (delegation && delegation.endDate! <= new Date()) {
      eligibility.canApproveOrSendBack = ag.approvals.some((app) => app.users?.some((u) => u.id === delegation.delegateToUserId) || delegation.id === app.position?.id);
      eligibility.canSubmit = ["PBR", "Planning, Bidding & Reporting"].includes(ag.approvals[0].as ?? "") && (ag.approvals[0].users ?? []).some((u) => u.id === delegation.delegateToUserId);
    } else {
      eligibility.canApproveOrSendBack = ag.approvals.some((app) => app.users?.some((u) => u.id === userLogin.id) || userLogin.position?.id === app.position?.id);
      eligibility.canSubmit = ["PBR", "Planning, Bidding & Reporting"].includes(ag.approvals[0].as ?? "") && (ag.approvals[0].users ?? []).some((u) => u.id === userLogin.id);
    }

  }

  if (atg && atg.sequence === 1) {
    eligibility.canSendBackToUPP = userLogin.id === atg.approvalTemplates[0].users![0].id;
  }

  return eligibility;
};

/**
 * Gets the display name for a sub-document type
 * @param documentName The sub-document type
 * @returns The human-readable name for the document type
 */
export const getSubDocumentWording = (documentName: TypeOf<typeof SubDocumentRequisition>): string => {
  switch (documentName) {
    case "REQUISITION":
      return "Requisition";

    case "OWNER_ESTIMATION":
      return "Owner Estimation";

    case "DA_JUSTIFICATION":
      return "DA Justification";

    case "HSE_RISK_ASSESSMENT":
      return "HSE Risk Assessment";

    case "INSURANCE_ASSESSMENT":
      return "Insurance Assessment";

    default:
      return "Requisition";
  }
};

export const isCurrentUserIsPBR = async (
  ctx: Context,
  userLogin: User,
  findApprovalGroup: FindApprovalGroup,
  docId: string,
  docType: TypeOf<typeof DocumentTemplate>
) => {
  let isCurrentUserPBR = false;
  const [approvalGroups] = await findApprovalGroup(ctx, { documentId: docId, documentType: docType });
  if (approvalGroups) {
    for (const approvalGroup of approvalGroups) {
      if (
        ["PBR", "Planning, Bidding & Reporting"].includes(approvalGroup.approvals[0].as ?? "") &&
        (approvalGroup.approvals[0].users ?? []).some((u) => u.id === userLogin.id)
      ) {
        isCurrentUserPBR = true;
      }
    }
  }

  return isCurrentUserPBR;
};

export const getUserApprovalReminder = async (ctx: Context, findApprovalGroup: FindApprovalGroup, docId: string, docType: TypeOf<typeof DocumentTemplate>) => {
  //
  // get the user approval for reminder notification
  const [approvaGroups] = await findApprovalGroup(ctx, { documentId: docId, documentType: docType, status: "PROCESSING" });
  const approvalGroup = approvaGroups.length > 0 ? approvaGroups[0] : null;
  let user = null;

  if (approvalGroup) {
    user = approvalGroup.approvals[0].currentUserInPosition;
  }

  return user;
};

export const getLastApprovalGroupDurationDays = async (
  ctx: Context,
  findApprovalGroup: FindApprovalGroup,
  approvalGroup: ApprovalGroup | null,
  now: Date,
  findCalendar: FindCalendar | null = null
): Promise<number> => {
  //
  if (approvalGroup === null || approvalGroup.sequence! === 1) {
    return 0;
  }

  // for testing
  // if (approvalGroup.approvals) {
  //   return 0;
  // }

  let sequence = approvalGroup.sequence! - 1;
  let [ag] = await findApprovalGroup(ctx, { id: `${approvalGroup.id?.slice(0, -3)}-${sequence.toString().padStart(2, "0")}` });
  let signedDate = ag[0].approvals[0].date;

  if (!signedDate) {
    return 0;
  }

  if (findCalendar) {
    //
    try {
      const date = new Date(signedDate).toISOString().split("T")[0];

      // Get list of dates until today
      const dateList = getDateListUntilToday(new Date(date));

      // Get holidays from the database
      const [holidays] = await findCalendar(ctx, { year: new Date(date).getFullYear() });

      // Convert holiday dates to 'YYYY-MM-DD' format for accurate comparison
      const holidaysDateList = holidays.map((holiday) =>
        holiday.date ? new Date(holiday.date).toISOString().split("T")[0] : ""
      );

      // filter weekends and holidays
      const result = dateList
        .map(date => new Date(date).toISOString().split("T")[0]) // Convert to YYYY-MM-DD
        .filter(date => !holidaysDateList.includes(date)) // Exclude holidays
        .filter(date => !isWeekend(new Date(date))); // Exclude weekends

      return result.length === 0 ? 0 : result.length - 1;

    } catch (error) {
      console.log("Failed to fetch Indonesian holidays: " + error);
      throw new Error("Failed to fetch Indonesian holidays: " + error);
    }
  }

  return Math.floor((now.getTime() - new Date(signedDate).getTime()) / (1000 * 60 * 60 * 24));
};

function dateToISOString(date: Date): Date {
  return new Date(date.toISOString().split("T")[0]); // Format YYYY-MM-DD
}

function getDateListUntilToday(startDateStr: Date): Date[] {
  const startDate = new Date(startDateStr);
  const today = new Date();
  const dateList: Date[] = [];

  while (startDate <= today) {
    dateList.push(new Date(startDate.toISOString().split("T")[0])); // Format YYYY-MM-DD
    startDate.setDate(startDate.getDate() + 1); // Move to the next day
  }

  return dateList;
}

function isWeekend(dateStr: Date): boolean {
  const date = new Date(dateStr);
  const day = date.getDay();
  return day === 0 || day === 6; // Sunday or Saturday
}

export const getCurrentUserPICNames = (approvalGroups: ApprovalGroup[]) => {
  //
  const lastApproval = approvalGroups.find((ag) => ag.status === "PROCESSING");
  let PICNames: string = "";

  if (lastApproval) {
    //
    for (const app of lastApproval.approvals) {
      // type user
      if (app.users && app.users.length > 0) {
        PICNames = app.users.map((user) => user.name).join(", ");
      }
      // type role
      else {
        PICNames = app.currentUserInPosition!.name!;
      }

    }
  }

  return PICNames;
}

export const activeDelegation = (approval: Approval): DelegationUser | null => {
  let delegationUser: DelegationUser | null = null;
  const now = new Date();
  
  // type users
  if (approval.users && approval.users.length > 0) {
    for (const user of approval.users) {
      if (
        user.delegation &&
        now >= new Date(user.delegation.startDate!) &&
        now <= new Date(user.delegation.endDate!)
      ) {
        delegationUser = user.delegation;
      }
    }
  }
  else {
    // type role
    if (
      approval.currentUserInPosition?.delegation &&
      now >= new Date(approval.currentUserInPosition.delegation.startDate!) &&
      now <= new Date(approval.currentUserInPosition.delegation.endDate!)
    ) {
      delegationUser = approval.currentUserInPosition.delegation;
    }
  }

  return delegationUser;
}

export const approvalUserActiveDelegation = (approvalUser: UserWithDelegation): DelegationUser | null => {
  // 
  const now = new Date();
  if (
    approvalUser.delegation &&
    now >= new Date(approvalUser.delegation.startDate!) &&
    now <= new Date(approvalUser.delegation.endDate!)
  ) {
    return approvalUser.delegation;
  }

  return null;
}

export const assignDelegation = async (
  ctx: Context,
  resultApprovalGroups: ApprovalGroup[],
  findUser: FindUser,
  findDelegation: FindDelegation,
): Promise<ApprovalGroup[]> => {
  // Assigns delegation users when create approval groups
  const [delegations] = await findDelegation(ctx, { type: "DELEGATION" });
  const activeDelegations = delegations.filter(
    d => d.startDate && d.endDate && 
    new Date() >= new Date(d.startDate) && 
    new Date() <= new Date(d.endDate)
  );

  if (activeDelegations.length === 0) {
    return resultApprovalGroups;
  }

  // assign active delegation to approval
  for (const activeDelegation of activeDelegations) {
    const [delegateToUsers] = await findUser(ctx, { ids: [activeDelegation.delegateToUserId] });
    const delegateToUser = delegateToUsers[0];
    
    if (!delegateToUser || delegateToUser === null) {
      continue;
    }

    // set user delegation
    const delegationUser: DelegationUser = {
      user: delegateToUser,
      startDate: activeDelegation.startDate,
      endDate: activeDelegation.endDate,
      remarks: activeDelegation.remarks,
      type: activeDelegation.type,
      syncAt: activeDelegation.syncAt,
    }

    for (const approvalGroups of resultApprovalGroups) {
      for (const approval of approvalGroups.approvals) {
        // type users
        if (approval.users) {
          for (const user of approval.users) {
            if (
              user.position && user.position.id === activeDelegation.id &&
              user.id !== activeDelegation.delegateToUserId
            ) {
              user.delegation = delegationUser;
            }
          }
        }
        // type role
        else {
          if (
            approval.currentUserInPosition?.position && 
            approval.currentUserInPosition.position.id === activeDelegation.id &&
            approval.currentUserInPosition.id !== activeDelegation.delegateToUserId
          ) {
            approval.currentUserInPosition.delegation = delegationUser;
          }
        }
      }
    }
  }

  return resultApprovalGroups;
}

export const hasOEApprovalUserDepartment = (currentApproval: Approval[], departmentId: string, ccEmails: string[]) : [User | null, string[]] => {
  // 
  const isBudgetOwner1 = budgetOwnerDepartments1.includes(departmentId); // <EMAIL> - 80141103 (Primary)
  const isBudgetOwner2 = budgetOwnerDepartments2.includes(departmentId); // <EMAIL> - 80131090 (Primary)

  let approvalUser = null;
  let ccEmail = ccEmails;

  if (isBudgetOwner1) {
    approvalUser = currentApproval.find((app) => app.users?.find((user) => user.id === "80141103"))?.users![0] ?? null;
    ccEmail.push(currentApproval.find((app) => app.users?.find((user) => user.id === "80131090"))?.users![0].email!);
  }
  else if (isBudgetOwner2) {
    approvalUser = currentApproval.find((app) => app.users?.find((user) => user.id === "80131090"))?.users![0] ?? null;
    ccEmail.push(currentApproval.find((app) => app.users?.find((user) => user.id === "80131090"))?.users![0].email!);
  }

  return [approvalUser, ccEmail];
}

