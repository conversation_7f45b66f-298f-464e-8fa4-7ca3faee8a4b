import { Usecase } from "../../framework/core.js";
import { FindOnePQVendor, SavePQVendor } from "../model/model_prequalification.js";

class Gateways {
  findOnePQVendor: FindOnePQVendor;
  savePQVendor: SavePQVendor;
}

export class Request {
  pqId: string;
  vendorId: number;
}

export class Response {}

export const vendorPQRequirementSubmit: Usecase<Gateways, Request, Response> = {
  gateways: Gateways,
  request: Request,
  setup: (o) => async (ctx, req) => {
    const pqVendor = await o.findOnePQVendor(ctx, { ...req, civdVendorId: req.vendorId });

    if (!pqVendor) {
      return {};
    }

    pqVendor.status = "REQUIREMENT_SUBMIT";

    await o.savePQVendor(ctx, pqVendor);

    return {};
  },
};
